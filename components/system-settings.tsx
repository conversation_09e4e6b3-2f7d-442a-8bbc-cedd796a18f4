"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Settings, 
  Database, 
  Shield, 
  Bell, 
  Palette, 
  Download, 
  Upload,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react"
import { toast } from "sonner"

interface SystemConfig {
  general: {
    siteName: string
    siteDescription: string
    maxFileSize: number
    allowedFileTypes: string[]
    autoBackup: boolean
    backupInterval: number
  }
  ai: {
    defaultModel: string
    maxTokens: number
    temperature: number
    timeout: number
  }
  security: {
    sessionTimeout: number
    maxLoginAttempts: number
    requireStrongPassword: boolean
    enableAuditLog: boolean
  }
  notifications: {
    emailNotifications: boolean
    systemAlerts: boolean
    reportCompletion: boolean
    errorAlerts: boolean
  }
}

const defaultConfig: SystemConfig = {
  general: {
    siteName: "温州医科大学 ETOCD 术前评估平台",
    siteDescription: "专业的医学影像分析和术前评估系统",
    maxFileSize: 100,
    allowedFileTypes: [".nii.gz"],
    autoBackup: true,
    backupInterval: 24
  },
  ai: {
    defaultModel: "gpt-3.5-turbo",
    maxTokens: 2000,
    temperature: 0.7,
    timeout: 30
  },
  security: {
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    requireStrongPassword: true,
    enableAuditLog: true
  },
  notifications: {
    emailNotifications: false,
    systemAlerts: true,
    reportCompletion: true,
    errorAlerts: true
  }
}

export function SystemSettings() {
  const [config, setConfig] = useState<SystemConfig>(defaultConfig)
  const [loading, setLoading] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // 加载配置
  const loadConfig = () => {
    try {
      const saved = localStorage.getItem('system-config')
      if (saved) {
        setConfig({ ...defaultConfig, ...JSON.parse(saved) })
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      toast.error('加载配置失败')
    }
  }

  // 保存配置
  const saveConfig = async () => {
    try {
      setLoading(true)
      localStorage.setItem('system-config', JSON.stringify(config))
      setLastSaved(new Date())
      toast.success('配置保存成功')
    } catch (error) {
      console.error('保存配置失败:', error)
      toast.error('保存配置失败')
    } finally {
      setLoading(false)
    }
  }

  // 重置配置
  const resetConfig = () => {
    if (confirm('确定要重置所有配置到默认值吗？')) {
      setConfig(defaultConfig)
      toast.success('配置已重置')
    }
  }

  // 导出配置
  const exportConfig = () => {
    try {
      const blob = new Blob([JSON.stringify(config, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `system-config-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('配置导出成功')
    } catch (error) {
      console.error('导出配置失败:', error)
      toast.error('导出配置失败')
    }
  }

  // 导入配置
  const importConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        setConfig({ ...defaultConfig, ...imported })
        toast.success('配置导入成功')
      } catch (error) {
        console.error('导入配置失败:', error)
        toast.error('配置文件格式错误')
      }
    }
    reader.readAsText(file)
  }

  useEffect(() => {
    loadConfig()
  }, [])

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">系统设置</h2>
          <p className="text-muted-foreground">管理系统配置和参数</p>
        </div>
        <div className="flex items-center space-x-2">
          {lastSaved && (
            <Badge variant="outline" className="text-xs">
              <CheckCircle className="mr-1 h-3 w-3" />
              上次保存: {lastSaved.toLocaleTimeString()}
            </Badge>
          )}
          <Button variant="outline" onClick={exportConfig}>
            <Download className="mr-2 h-4 w-4" />
            导出配置
          </Button>
          <Button variant="outline" onClick={() => document.getElementById('import-config')?.click()}>
            <Upload className="mr-2 h-4 w-4" />
            导入配置
          </Button>
          <input
            id="import-config"
            type="file"
            accept=".json"
            onChange={importConfig}
            className="hidden"
          />
          <Button variant="outline" onClick={resetConfig}>
            <RefreshCw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button onClick={saveConfig} disabled={loading}>
            {loading ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : <Settings className="mr-2 h-4 w-4" />}
            保存配置
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">
            <Settings className="mr-2 h-4 w-4" />
            常规设置
          </TabsTrigger>
          <TabsTrigger value="ai">
            <Database className="mr-2 h-4 w-4" />
            AI配置
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="mr-2 h-4 w-4" />
            安全设置
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            通知设置
          </TabsTrigger>
        </TabsList>

        {/* 常规设置 */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>常规设置</CardTitle>
              <CardDescription>配置系统的基本参数和行为</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="siteName">站点名称</Label>
                  <Input
                    id="siteName"
                    value={config.general.siteName}
                    onChange={(e) => setConfig({
                      ...config,
                      general: { ...config.general, siteName: e.target.value }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxFileSize">最大文件大小 (MB)</Label>
                  <Input
                    id="maxFileSize"
                    type="number"
                    value={config.general.maxFileSize}
                    onChange={(e) => setConfig({
                      ...config,
                      general: { ...config.general, maxFileSize: parseInt(e.target.value) || 100 }
                    })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="siteDescription">站点描述</Label>
                <Textarea
                  id="siteDescription"
                  value={config.general.siteDescription}
                  onChange={(e) => setConfig({
                    ...config,
                    general: { ...config.general, siteDescription: e.target.value }
                  })}
                  rows={3}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>自动备份</Label>
                    <p className="text-sm text-muted-foreground">定期自动备份系统数据</p>
                  </div>
                  <Switch
                    checked={config.general.autoBackup}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      general: { ...config.general, autoBackup: checked }
                    })}
                  />
                </div>

                {config.general.autoBackup && (
                  <div className="space-y-2">
                    <Label htmlFor="backupInterval">备份间隔 (小时)</Label>
                    <Input
                      id="backupInterval"
                      type="number"
                      value={config.general.backupInterval}
                      onChange={(e) => setConfig({
                        ...config,
                        general: { ...config.general, backupInterval: parseInt(e.target.value) || 24 }
                      })}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI配置 */}
        <TabsContent value="ai">
          <Card>
            <CardHeader>
              <CardTitle>AI配置</CardTitle>
              <CardDescription>配置AI模型和相关参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  这些设置将影响AI问答系统的默认行为。用户仍可在聊天界面中覆盖这些设置。
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="defaultModel">默认模型</Label>
                  <Select
                    value={config.ai.defaultModel}
                    onValueChange={(value) => setConfig({
                      ...config,
                      ai: { ...config.ai, defaultModel: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                      <SelectItem value="gpt-4">GPT-4</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxTokens">最大令牌数</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    value={config.ai.maxTokens}
                    onChange={(e) => setConfig({
                      ...config,
                      ai: { ...config.ai, maxTokens: parseInt(e.target.value) || 2000 }
                    })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="temperature">温度 (0-1)</Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    min="0"
                    max="1"
                    value={config.ai.temperature}
                    onChange={(e) => setConfig({
                      ...config,
                      ai: { ...config.ai, temperature: parseFloat(e.target.value) || 0.7 }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeout">超时时间 (秒)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={config.ai.timeout}
                    onChange={(e) => setConfig({
                      ...config,
                      ai: { ...config.ai, timeout: parseInt(e.target.value) || 30 }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 安全设置 */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>配置系统安全相关参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  修改安全设置可能影响系统的安全性，请谨慎操作。
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时 (分钟)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={config.security.sessionTimeout}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, sessionTimeout: parseInt(e.target.value) || 30 }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">最大登录尝试次数</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={config.security.maxLoginAttempts}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, maxLoginAttempts: parseInt(e.target.value) || 5 }
                    })}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>强密码要求</Label>
                    <p className="text-sm text-muted-foreground">要求用户使用强密码</p>
                  </div>
                  <Switch
                    checked={config.security.requireStrongPassword}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      security: { ...config.security, requireStrongPassword: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>启用审计日志</Label>
                    <p className="text-sm text-muted-foreground">记录用户操作日志</p>
                  </div>
                  <Switch
                    checked={config.security.enableAuditLog}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      security: { ...config.security, enableAuditLog: checked }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 通知设置 */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置系统通知和提醒</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>邮件通知</Label>
                    <p className="text-sm text-muted-foreground">发送重要事件的邮件通知</p>
                  </div>
                  <Switch
                    checked={config.notifications.emailNotifications}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      notifications: { ...config.notifications, emailNotifications: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>系统警报</Label>
                    <p className="text-sm text-muted-foreground">显示系统状态警报</p>
                  </div>
                  <Switch
                    checked={config.notifications.systemAlerts}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      notifications: { ...config.notifications, systemAlerts: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>报告完成通知</Label>
                    <p className="text-sm text-muted-foreground">CT分析完成时发送通知</p>
                  </div>
                  <Switch
                    checked={config.notifications.reportCompletion}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      notifications: { ...config.notifications, reportCompletion: checked }
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>错误警报</Label>
                    <p className="text-sm text-muted-foreground">系统错误时发送警报</p>
                  </div>
                  <Switch
                    checked={config.notifications.errorAlerts}
                    onCheckedChange={(checked) => setConfig({
                      ...config,
                      notifications: { ...config.notifications, errorAlerts: checked }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
