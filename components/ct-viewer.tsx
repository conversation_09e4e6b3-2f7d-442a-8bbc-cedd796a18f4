"use client"

import { useR<PERSON>, useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { RotateCcw, ZoomIn, ZoomOut, Move, Square } from "lucide-react"

// 模拟nifti库的基本功能
const nifti = {
  readHeader: (buffer: ArrayBuffer) => {
    // 简化的NIFTI头部读取 - 实际应用中需要完整实现
    const view = new DataView(buffer);
    const magic = new TextDecoder().decode(buffer.slice(344, 348));
    
    if (magic !== "n+1\0" && magic !== "ni1\0") {
      return null;
    }
    
    return {
      dims: [3, 512, 512, 141, 1, 1, 1, 1], // 示例维度
      datatypeCode: 16, // Float32
      pixDims: [-1, 0.40625, 0.40625, 0.699999988079071, 1, 1, 1, 1],
      vox_offset: 352
    };
  },
  
  readImage: (header: any, buffer: ArrayBuffer) => {
    return buffer.slice(header.vox_offset);
  }
};

interface CtViewerProps {
  file: File
}

type ViewAxis = "axial" | "coronal" | "sagittal"
type TypedPixelData = Float32Array | Uint16Array | Int16Array | Uint8Array

// 根据NIFTI数据类型码获取TypedArray构造函数
function getTypedArrayConstructor(datatypeCode: number) {
  switch(datatypeCode) {
    case 2: return Uint8Array;    // DT_UNSIGNED_CHAR
    case 4: return Int16Array;    // DT_SIGNED_SHORT  
    case 8: return Int32Array;    // DT_SIGNED_INT
    case 16: return Float32Array; // DT_FLOAT
    case 64: return Float64Array; // DT_DOUBLE
    case 256: return Int8Array;   // DT_INT8
    case 512: return Uint16Array; // DT_UINT16
    case 768: return Uint32Array; // DT_UINT32
    default: 
      console.warn(`Unknown datatype code: ${datatypeCode}, defaulting to Float32Array`);
      return Float32Array;
  }
}

// 解压gzip数据的简单实现
async function decompressGzip(buffer: ArrayBuffer): Promise<ArrayBuffer> {
  if (typeof window !== 'undefined' && 'DecompressionStream' in window) {
    const stream = new DecompressionStream('gzip');
    const writer = stream.writable.getWriter();
    const reader = stream.readable.getReader();
    
    writer.write(new Uint8Array(buffer));
    writer.close();
    
    const chunks: Uint8Array[] = [];
    let result;
    while (!(result = await reader.read()).done) {
      chunks.push(result.value);
    }
    
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const decompressed = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks) {
      decompressed.set(chunk, offset);
      offset += chunk.length;
    }
    
    return decompressed.buffer;
  }
  
  // 如果浏览器不支持DecompressionStream，返回原始buffer
  // 在实际应用中，你可能需要使用pako库来解压
  console.warn('DecompressionStream not supported, assuming uncompressed data');
  return buffer;
}

export function CtViewer({ file }: CtViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [niftiHeader, setNiftiHeader] = useState<any>(null)
  const [pixelData, setPixelData] = useState<TypedPixelData | null>(null)
  const [slice, setSlice] = useState(0)
  const [maxSlice, setMaxSlice] = useState(0)
  const [viewAxis, setViewAxis] = useState<ViewAxis>("axial")
  const [windowCenter, setWindowCenter] = useState(127)
  const [windowWidth, setWindowWidth] = useState(255)
  const [globalMinPixelValue, setGlobalMinPixelValue] = useState(0)
  const [globalMaxPixelValue, setGlobalMaxPixelValue] = useState(255)
  const [loadingError, setLoadingError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [panX, setPanX] = useState(0)
  const [panY, setPanY] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 })

  useEffect(() => {
    if (!file) {
      setLoadingError(null)
      setNiftiHeader(null)
      setPixelData(null)
      return
    }
    
    setLoadingError(null)
    setNiftiHeader(null)
    setPixelData(null)
    setIsLoading(true)
    
    console.log(`Processing file: ${file.name}, type: ${file.type}, size: ${file.size} bytes`)

    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        let arrayBuffer = e.target?.result as ArrayBuffer
        if (!arrayBuffer || arrayBuffer.byteLength === 0) {
          setLoadingError("Failed to read file: ArrayBuffer is empty.")
          return
        }
        
        console.log("File read into ArrayBuffer, length:", arrayBuffer.byteLength)
        
        // 检查是否是gzip压缩文件
        const firstBytes = new Uint8Array(arrayBuffer.slice(0, 10))
        console.log(
          "First 10 bytes (hex):",
          Array.from(firstBytes)
            .map((b) => b.toString(16).padStart(2, "0"))
            .join(" "),
        )
        
        // 如果是gzip文件，尝试解压
        if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
          console.log("Detected gzip compression, attempting to decompress...")
          try {
            arrayBuffer = await decompressGzip(arrayBuffer)
            console.log("Decompressed size:", arrayBuffer.byteLength)
          } catch (err) {
            console.error("Decompression failed:", err)
            setLoadingError("Failed to decompress gzip file. Your browser might not support decompression.")
            return
          }
        }

        const header = nifti.readHeader(arrayBuffer)
        if (!header) {
          setLoadingError("NIFTI header parsing failed. The file might be corrupted or not a valid NIFTI format.")
          return
        }
        
        console.log("NIFTI Header successfully read:", header)
        console.log("NIFTI Dimensions:", header.dims)

        const imageBuffer = nifti.readImage(header, arrayBuffer)
        if (!imageBuffer || imageBuffer.byteLength === 0) {
          setLoadingError("NIFTI image data is empty or invalid.")
          return
        }
        
        console.log("NIFTI Image data read, length:", imageBuffer.byteLength)

        // 使用修复后的类型数组构造函数获取方法
        const TypedArrayConstructor = getTypedArrayConstructor(header.datatypeCode)
        const actualPixelData = new TypedArrayConstructor(imageBuffer) as TypedPixelData

        if (!actualPixelData || actualPixelData.length === 0) {
          setLoadingError("Failed to create typed pixel data from NIFTI image.")
          return
        }
        
        console.log("Pixel Data Length:", actualPixelData.length)
        console.log("Pixel Data Constructor:", TypedArrayConstructor.name)

        setNiftiHeader(header)
        setPixelData(actualPixelData)

        // 计算全局最小最大值
        let minVal = Number.POSITIVE_INFINITY
        let maxVal = Number.NEGATIVE_INFINITY
        for (let i = 0; i < actualPixelData.length; i++) {
          if (actualPixelData[i] < minVal) minVal = actualPixelData[i]
          if (actualPixelData[i] > maxVal) maxVal = actualPixelData[i]
        }
        setGlobalMinPixelValue(minVal)
        setGlobalMaxPixelValue(maxVal)
        
        // 设置默认窗宽窗位
        const range = maxVal - minVal
        setWindowCenter(minVal + range / 2)
        setWindowWidth(range)
        
        console.log("Global Min/Max Pixel Value:", minVal, maxVal)
        
      } catch (err: any) {
        let detailedMessage = `Error processing NIFTI file: ${err.message}.`
        if (file.name.endsWith(".gz")) {
          detailedMessage += " This could be an issue with GZ decompression or the underlying NIFTI data."
        }
        setLoadingError(detailedMessage)
        console.error("Error processing NIFTI file:", err)
      } finally {
        setIsLoading(false)
      }
    }
    
    reader.onerror = () => {
      setLoadingError("Error reading file with FileReader.")
      setIsLoading(false)
    }
    
    reader.readAsArrayBuffer(file)
  }, [file])

  // Update slice parameters when header or viewAxis changes
  useEffect(() => {
    if (!niftiHeader) {
      setMaxSlice(0)
      setSlice(0)
      return
    }

    const dims = niftiHeader.dims
    let newMaxSlice = 0
    switch (viewAxis) {
      case "axial":
        newMaxSlice = dims[3] > 0 ? dims[3] - 1 : 0
        break
      case "coronal":
        newMaxSlice = dims[2] > 0 ? dims[2] - 1 : 0
        break
      case "sagittal":
        newMaxSlice = dims[1] > 0 ? dims[1] - 1 : 0
        break
    }
    setMaxSlice(newMaxSlice)
    setSlice(Math.floor(newMaxSlice / 2))
  }, [niftiHeader, viewAxis])

  // Draw slice
  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    
    if (niftiHeader && pixelData && canvas && container && pixelData.length > 0) {
      const currentValidSlice = Math.min(Math.max(0, slice), maxSlice)
      drawSlice(canvas, niftiHeader, pixelData, currentValidSlice, viewAxis, windowCenter, windowWidth)
    } else if (canvas) {
      const ctx = canvas.getContext("2d")
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        if (loadingError) {
          drawErrorMessage(ctx, canvas, loadingError)
        } else if (isLoading) {
          drawLoadingMessage(ctx, canvas)
        }
      }
    }
  }, [niftiHeader, pixelData, slice, viewAxis, maxSlice, windowCenter, windowWidth, loadingError, isLoading, zoom])

  const drawSlice = (
    canvas: HTMLCanvasElement,
    header: any,
    imgData: TypedPixelData,
    sliceIndex: number,
    axis: ViewAxis,
    wCenter: number,
    wWidth: number,
  ) => {
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const dims = header.dims
    const [dim1, dim2, dim3] = [dims[1], dims[2], dims[3]]

    let sliceWidth, sliceHeight
    switch (axis) {
      case "axial":
        sliceWidth = dim1
        sliceHeight = dim2
        break
      case "coronal":
        sliceWidth = dim1
        sliceHeight = dim3
        break
      case "sagittal":
        sliceWidth = dim2
        sliceHeight = dim3
        break
      default:
        return
    }

    if (sliceWidth <= 0 || sliceHeight <= 0) {
      console.warn(`Invalid dimensions for ${axis} view: ${sliceWidth}x${sliceHeight}`)
      return
    }

    // 设置canvas尺寸，考虑缩放
    const displayWidth = Math.floor(sliceWidth * zoom)
    const displayHeight = Math.floor(sliceHeight * zoom)
    
    canvas.width = displayWidth
    canvas.height = displayHeight
    canvas.style.width = `${displayWidth}px`
    canvas.style.height = `${displayHeight}px`

    const imageData = ctx.createImageData(displayWidth, displayHeight)
    
    // 窗宽窗位计算
    const windowMin = wCenter - wWidth / 2
    const windowMax = wCenter + wWidth / 2

    for (let j = 0; j < displayHeight; j++) {
      for (let i = 0; i < displayWidth; i++) {
        // 考虑缩放的采样
        const srcI = Math.floor(i / zoom)
        const srcJ = Math.floor(j / zoom)
        
        let xVol: number, yVol: number, zVol: number

        switch (axis) {
          case "axial":
            xVol = srcI
            yVol = srcJ
            zVol = sliceIndex
            break
          case "coronal":
            xVol = srcI
            yVol = sliceIndex
            zVol = srcJ
            break
          case "sagittal":
            xVol = sliceIndex
            yVol = srcI
            zVol = srcJ
            break
          default:
            continue
        }

        let pixelValue = 0
        if (xVol >= 0 && xVol < dim1 && yVol >= 0 && yVol < dim2 && zVol >= 0 && zVol < dim3) {
          const volumeIndex = zVol * dim1 * dim2 + yVol * dim1 + xVol
          if (volumeIndex >= 0 && volumeIndex < imgData.length) {
            pixelValue = imgData[volumeIndex]
          }
        }

        // 应用窗宽窗位
        let normalizedValue = 0
        if (windowMax > windowMin) {
          normalizedValue = Math.max(0, Math.min(255, ((pixelValue - windowMin) / (windowMax - windowMin)) * 255))
        }

        const pixelIndex = (j * displayWidth + i) * 4
        imageData.data[pixelIndex] = normalizedValue     // R
        imageData.data[pixelIndex + 1] = normalizedValue // G
        imageData.data[pixelIndex + 2] = normalizedValue // B
        imageData.data[pixelIndex + 3] = 255            // A
      }
    }
    
    ctx.putImageData(imageData, 0, 0)
  }

  const drawErrorMessage = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, message: string) => {
    ctx.fillStyle = "red"
    ctx.font = "12px Arial"
    ctx.textAlign = "center"
    const words = message.split(" ")
    let line = ""
    let y = canvas.height / 2 - 20
    const lineHeight = 16
    
    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + " "
      const metrics = ctx.measureText(testLine)
      if (metrics.width > canvas.width * 0.9 && n > 0) {
        ctx.fillText(line, canvas.width / 2, y)
        line = words[n] + " "
        y += lineHeight
      } else {
        line = testLine
      }
    }
    ctx.fillText(line, canvas.width / 2, y)
  }

  const drawLoadingMessage = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => {
    ctx.fillStyle = "black"
    ctx.font = "16px Arial"
    ctx.textAlign = "center"
    ctx.fillText("Loading NIFTI data...", canvas.width / 2, canvas.height / 2)
  }

  const resetWindowLevel = () => {
    const range = globalMaxPixelValue - globalMinPixelValue
    setWindowCenter(globalMinPixelValue + range / 2)
    setWindowWidth(range)
  }

  const resetView = () => {
    setZoom(1)
    setPanX(0)
    setPanY(0)
    resetWindowLevel()
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDragging(true)
    setLastMousePos({ x: e.clientX, y: e.clientY })
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging) return

    const deltaX = e.clientX - lastMousePos.x
    const deltaY = e.clientY - lastMousePos.y

    setPanX(prev => prev + deltaX)
    setPanY(prev => prev + deltaY)
    setLastMousePos({ x: e.clientX, y: e.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault()
    const delta = e.deltaY > 0 ? -0.1 : 0.1
    setZoom(prev => Math.max(0.1, Math.min(5, prev + delta)))
  }

  return (
    <div className="space-y-4">
      {/* 影像信息 */}
      {niftiHeader && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">影像信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <span className="text-muted-foreground">维度:</span>
                <Badge variant="outline" className="ml-2">
                  {niftiHeader.dims[1]} × {niftiHeader.dims[2]} × {niftiHeader.dims[3]}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">像素范围:</span>
                <Badge variant="outline" className="ml-2">
                  {globalMinPixelValue.toFixed(1)} - {globalMaxPixelValue.toFixed(1)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div
        ref={containerRef}
        className="w-full border rounded-md bg-gray-100 overflow-auto relative"
        style={{ maxHeight: "600px" }}
      >
        <canvas
          ref={canvasRef}
          className="block mx-auto cursor-move"
          style={{
            imageRendering: "pixelated",
            transform: `translate(${panX}px, ${panY}px)`,
            cursor: isDragging ? 'grabbing' : 'grab'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
        />

        {/* 工具栏 */}
        <div className="absolute top-2 right-2 flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setZoom(prev => Math.min(5, prev + 0.2))}
            disabled={!pixelData}
          >
            <ZoomIn className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setZoom(prev => Math.max(0.1, prev - 0.2))}
            disabled={!pixelData}
          >
            <ZoomOut className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={resetView}
            disabled={!pixelData}
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      {loadingError && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">错误: {loadingError}</p>
        </div>
      )}
      
      {isLoading && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-600">正在加载 NIFTI 文件...</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 左侧控制 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Label className="min-w-[100px]">
              切片: {pixelData ? `${slice} / ${maxSlice}` : "N/A"}
            </Label>
            <Select
              onValueChange={(value: ViewAxis) => setViewAxis(value)}
              defaultValue={viewAxis}
              disabled={!pixelData}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="选择视图" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="axial">轴状位</SelectItem>
                <SelectItem value="coronal">冠状位</SelectItem>
                <SelectItem value="sagittal">矢状位</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Slider
            min={0}
            max={maxSlice}
            step={1}
            value={[slice]}
            onValueChange={(value) => setSlice(value[0])}
            disabled={!pixelData || maxSlice === 0}
          />

          <div className="space-y-2">
            <Label>缩放: {zoom.toFixed(1)}x</Label>
            <Slider
              min={0.5}
              max={5}
              step={0.1}
              value={[zoom]}
              onValueChange={(value) => setZoom(value[0])}
              disabled={!pixelData}
            />
          </div>
        </div>

        {/* 右侧窗宽窗位控制 */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>窗位: {Math.round(windowCenter)}</Label>
            <Slider
              min={globalMinPixelValue}
              max={globalMaxPixelValue}
              step={1}
              value={[windowCenter]}
              onValueChange={(value) => setWindowCenter(value[0])}
              disabled={!pixelData}
            />
          </div>
          
          <div className="space-y-2">
            <Label>窗宽: {Math.round(windowWidth)}</Label>
            <Slider
              min={1}
              max={(globalMaxPixelValue - globalMinPixelValue) * 2}
              step={1}
              value={[windowWidth]}
              onValueChange={(value) => setWindowWidth(value[0])}
              disabled={!pixelData}
            />
          </div>
          
          <Button 
            onClick={resetWindowLevel}
            disabled={!pixelData}
            variant="outline"
            size="sm"
          >
            重置窗宽窗位
          </Button>
        </div>
      </div>
      
      {pixelData && (
        <div className="text-xs text-gray-500 grid grid-cols-2 gap-2">
          <div>图像尺寸: {niftiHeader?.dims?.slice(1, 4).join(' × ')}</div>
          <div>像素范围: {Math.round(globalMinPixelValue)} ~ {Math.round(globalMaxPixelValue)}</div>
        </div>
      )}
    </div>
  )
}
