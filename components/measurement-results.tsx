"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Ruler, 
  Box, 
  Target, 
  Download, 
  Eye,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react"
import { MeasurementResult } from "@/lib/types"

interface MeasurementResultsProps {
  measurements: MeasurementResult[]
  isLoading?: boolean
  onExport?: () => void
  onVisualize?: (measurementId: string) => void
}

export function MeasurementResults({ 
  measurements, 
  isLoading = false, 
  onExport, 
  onVisualize 
}: MeasurementResultsProps) {
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Ruler className="h-5 w-5" />
            <span>测量计算中...</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={65} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              正在计算物理尺寸和表面积...
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (measurements.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Ruler className="h-5 w-5" />
            <span>测量结果</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Info className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">暂无测量数据</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1000) {
      return `${(volume / 1000).toFixed(2)} mL`
    }
    return `${volume.toFixed(2)} mm³`
  }

  const formatArea = (area: number) => {
    if (area >= 100) {
      return `${(area / 100).toFixed(2)} cm²`
    }
    return `${area.toFixed(2)} mm²`
  }

  const getQualityBadge = (quality: MeasurementResult['quality']) => {
    if (quality.isWatertight && quality.smoothness > 0.8) {
      return <Badge variant="default" className="text-xs">优秀</Badge>
    } else if (quality.isWatertight && quality.smoothness > 0.6) {
      return <Badge variant="secondary" className="text-xs">良好</Badge>
    } else {
      return <Badge variant="destructive" className="text-xs">需改进</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 概览卡片 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Ruler className="h-5 w-5" />
                <span>测量结果</span>
              </CardTitle>
              <CardDescription>
                基于分割掩码的精确物理尺寸计算
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              {onVisualize && (
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  3D可视化
                </Button>
              )}
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="mr-2 h-4 w-4" />
                  导出结果
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {measurements.length}
              </div>
              <div className="text-sm text-muted-foreground">检测到的结构</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {measurements.reduce((sum, m) => sum + m.measurements.volume.physicalVolumeML, 0).toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">总体积 (mL)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {measurements.filter(m => m.quality.isWatertight).length}
              </div>
              <div className="text-sm text-muted-foreground">高质量网格</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 详细结果 */}
      <Tabs defaultValue="summary" className="w-full">
        <TabsList>
          <TabsTrigger value="summary">概览</TabsTrigger>
          <TabsTrigger value="detailed">详细数据</TabsTrigger>
          <TabsTrigger value="quality">质量评估</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          {measurements.map((measurement) => (
            <Card key={measurement.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {measurement.labelName}
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    {getQualityBadge(measurement.quality)}
                    <Badge variant="outline" className="text-xs">
                      ID: {measurement.labelId}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* 体积 */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Box className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">体积</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">
                      {formatVolume(measurement.measurements.volume.physicalVolume)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {measurement.measurements.volume.voxelCount.toLocaleString()} 体素
                    </div>
                  </div>

                  {/* 表面积 */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Ruler className="h-4 w-4 text-green-500" />
                      <span className="font-medium">表面积</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {formatArea(measurement.measurements.surfaceArea.physicalArea)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {measurement.quality.meshFaces.toLocaleString()} 三角面
                    </div>
                  </div>

                  {/* 尺寸 */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Target className="h-4 w-4 text-orange-500" />
                      <span className="font-medium">边界框</span>
                    </div>
                    <div className="text-sm font-mono">
                      {measurement.measurements.boundingBox.physicalSize[0].toFixed(1)} ×{' '}
                      {measurement.measurements.boundingBox.physicalSize[1].toFixed(1)} ×{' '}
                      {measurement.measurements.boundingBox.physicalSize[2].toFixed(1)} mm
                    </div>
                    <div className="text-sm text-muted-foreground">
                      质心: ({measurement.measurements.centroid.physicalCoords[0].toFixed(1)}, {' '}
                      {measurement.measurements.centroid.physicalCoords[1].toFixed(1)}, {' '}
                      {measurement.measurements.centroid.physicalCoords[2].toFixed(1)}) mm
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="detailed">
          <Card>
            <CardHeader>
              <CardTitle>详细测量数据</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>结构</TableHead>
                    <TableHead>体积 (mm³)</TableHead>
                    <TableHead>体积 (mL)</TableHead>
                    <TableHead>表面积 (mm²)</TableHead>
                    <TableHead>表面积 (cm²)</TableHead>
                    <TableHead>体素数</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {measurements.map((measurement) => (
                    <TableRow key={measurement.id}>
                      <TableCell className="font-medium">
                        {measurement.labelName}
                      </TableCell>
                      <TableCell>
                        {measurement.measurements.volume.physicalVolume.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        {measurement.measurements.volume.physicalVolumeML.toFixed(3)}
                      </TableCell>
                      <TableCell>
                        {measurement.measurements.surfaceArea.physicalArea.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        {measurement.measurements.surfaceArea.physicalAreaCM2.toFixed(3)}
                      </TableCell>
                      <TableCell>
                        {measurement.measurements.volume.voxelCount.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quality">
          <Card>
            <CardHeader>
              <CardTitle>网格质量评估</CardTitle>
              <CardDescription>
                基于Marching Cubes算法生成的三角网格质量分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {measurements.map((measurement) => (
                  <div key={measurement.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{measurement.labelName}</h4>
                      {getQualityBadge(measurement.quality)}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">顶点数</div>
                        <div className="font-medium">
                          {measurement.quality.meshVertices.toLocaleString()}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">三角面数</div>
                        <div className="font-medium">
                          {measurement.quality.meshFaces.toLocaleString()}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">水密性</div>
                        <div className="flex items-center space-x-1">
                          {measurement.quality.isWatertight ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          )}
                          <span className="font-medium">
                            {measurement.quality.isWatertight ? '是' : '否'}
                          </span>
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">平滑度</div>
                        <div className="font-medium">
                          {(measurement.quality.smoothness * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
