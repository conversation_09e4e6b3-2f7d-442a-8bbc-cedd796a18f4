import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ReportData {
  patientInfo: {
    id: string
    name: string
    age: number
    gender: string
  }
  analysisResults: {
    difficulty: "低" | "中" | "高"
    score: number
    keyFindings: string[]
    recommendations: string
  }
  summary: string
}

interface AssessmentReportProps {
  data: ReportData
}

export function AssessmentReport({ data }: AssessmentReportProps) {
  const { patientInfo, analysisResults } = data

  const getDifficultyBadgeVariant = (difficulty: "低" | "中" | "高") => {
    switch (difficulty) {
      case "低":
        return "default"
      case "中":
        return "secondary"
      case "高":
        return "destructive"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>术前评估报告</CardTitle>
        <CardDescription>患者ID: {patientInfo.id}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="font-semibold">患者信息</h3>
          <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
            <p>姓名: {patientInfo.name}</p>
            <p>年龄: {patientInfo.age}</p>
            <p>性别: {patientInfo.gender}</p>
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="font-semibold">分析结果</h3>
          <div className="text-sm text-muted-foreground space-y-2">
            <div className="flex items-center gap-2">
              <span>手术难度:</span>
              <Badge variant={getDifficultyBadgeVariant(analysisResults.difficulty)}>
                {analysisResults.difficulty}
              </Badge>
            </div>
            <p>风险评分: {analysisResults.score}</p>
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="font-semibold">关键发现</h3>
          <ul className="list-disc list-inside text-sm text-muted-foreground">
            {analysisResults.keyFindings.map((finding, index) => (
              <li key={index}>{finding}</li>
            ))}
          </ul>
        </div>
        <div className="space-y-2">
          <h3 className="font-semibold">手术建议</h3>
          <p className="text-sm text-muted-foreground">{analysisResults.recommendations}</p>
        </div>
      </CardContent>
    </Card>
  )
}
