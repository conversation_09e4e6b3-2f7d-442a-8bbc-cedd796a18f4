"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Eye, Download, Filter } from "lucide-react"
import { Report, ApiResponse, PaginatedResponse } from "@/lib/types"
import { toast } from "sonner"
import { AssessmentReport } from "./assessment-report"
import { ReportListSkeleton } from "@/components/loading-states"

export function ReportList() {
  const searchParams = useSearchParams()
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [filters, setFilters] = useState({
    status: "",
    difficulty: "",
    dateFrom: "",
    dateTo: ""
  })

  // 获取报告列表
  const fetchReports = async (page = 1, search = "") => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        search,
        sortBy: "createdAt",
        sortOrder: "desc"
      })

      // 添加筛选参数
      if (filters.status) params.append("status", filters.status)
      if (filters.difficulty) params.append("difficulty", filters.difficulty)
      if (filters.dateFrom) params.append("dateFrom", filters.dateFrom)
      if (filters.dateTo) params.append("dateTo", filters.dateTo)
      
      // 从URL参数获取患者ID筛选
      const patientId = searchParams?.get("patientId")
      if (patientId) params.append("patientId", patientId)

      const response = await fetch(`/api/reports?${params}`)
      const result: ApiResponse<PaginatedResponse<Report>> = await response.json()

      if (result.success && result.data) {
        setReports(result.data.data)
        setTotalPages(result.data.totalPages)
        setCurrentPage(result.data.page)
      } else {
        toast.error(result.error || "获取报告列表失败")
      }
    } catch (error) {
      console.error("获取报告列表失败:", error)
      toast.error("获取报告列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
    fetchReports(1, value)
  }

  // 筛选处理
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    setCurrentPage(1)
    fetchReports(1, searchTerm)
  }

  // 查看报告详情
  const viewReportDetail = (report: Report) => {
    setSelectedReport(report)
    setIsDetailDialogOpen(true)
  }

  // 下载报告
  const downloadReport = async (report: Report) => {
    try {
      const reportData = {
        id: report.id,
        patientInfo: report.patientInfo,
        analysisResults: report.analysisResults,
        summary: report.summary,
        createdAt: report.createdAt,
        status: report.status
      }

      const blob = new Blob([JSON.stringify(reportData, null, 2)], {
        type: "application/json"
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `报告_${report.patientInfo.name}_${report.id}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast.success("报告下载成功")
    } catch (error) {
      console.error("下载报告失败:", error)
      toast.error("下载报告失败")
    }
  }

  // 获取状态徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="secondary">待处理</Badge>
      case "completed":
        return <Badge variant="default">已完成</Badge>
      case "reviewed":
        return <Badge variant="outline">已审核</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 获取难度徽章样式
  const getDifficultyBadge = (difficulty: string) => {
    switch (difficulty) {
      case "低":
        return <Badge variant="default">低</Badge>
      case "中":
        return <Badge variant="secondary">中</Badge>
      case "高":
        return <Badge variant="destructive">高</Badge>
      default:
        return <Badge variant="secondary">{difficulty}</Badge>
    }
  }

  useEffect(() => {
    fetchReports()
  }, [searchParams])

  return (
    <div className="space-y-4">
      {/* 搜索和筛选 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索患者姓名或报告ID..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部状态</SelectItem>
              <SelectItem value="pending">待处理</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="reviewed">已审核</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filters.difficulty} onValueChange={(value) => handleFilterChange("difficulty", value)}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="难度" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部难度</SelectItem>
              <SelectItem value="低">低</SelectItem>
              <SelectItem value="中">中</SelectItem>
              <SelectItem value="高">高</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 报告列表 */}
      <Card>
        <CardHeader>
          <CardTitle>评估报告列表</CardTitle>
          <CardDescription>查看和管理所有的术前评估报告</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <ReportListSkeleton />
          ) : reports.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">暂无报告数据</div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>报告ID</TableHead>
                  <TableHead>患者姓名</TableHead>
                  <TableHead>手术难度</TableHead>
                  <TableHead>风险评分</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="font-mono text-xs">{report.id}</TableCell>
                    <TableCell className="font-medium">{report.patientInfo.name}</TableCell>
                    <TableCell>{getDifficultyBadge(report.analysisResults.difficulty)}</TableCell>
                    <TableCell>{report.analysisResults.score}</TableCell>
                    <TableCell>{getStatusBadge(report.status)}</TableCell>
                    <TableCell>{new Date(report.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewReportDetail(report)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => downloadReport(report)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchReports(currentPage - 1, searchTerm)}
                disabled={currentPage <= 1}
              >
                上一页
              </Button>
              <span className="text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchReports(currentPage + 1, searchTerm)}
                disabled={currentPage >= totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 报告详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>评估报告详情</DialogTitle>
            <DialogDescription>
              报告ID: {selectedReport?.id} | 创建时间: {selectedReport?.createdAt ? new Date(selectedReport.createdAt).toLocaleString() : ''}
            </DialogDescription>
          </DialogHeader>
          {selectedReport && (
            <div className="py-4">
              <AssessmentReport data={selectedReport} />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
