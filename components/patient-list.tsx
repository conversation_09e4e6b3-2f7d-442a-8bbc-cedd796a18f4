"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Search, Edit, Trash2, Eye, FileText } from "lucide-react"
import { Patient, ApiResponse, PaginatedResponse } from "@/lib/types"
import { toast } from "sonner"

export function PatientList() {
  const [patients, setPatients] = useState<Patient[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    age: "",
    gender: "男" as "男" | "女",
    phone: "",
    idCard: "",
    address: "",
    medicalHistory: "",
    allergies: ""
  })

  // 获取患者列表
  const fetchPatients = async (page = 1, search = "") => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        search,
        sortBy: "createdAt",
        sortOrder: "desc"
      })

      const response = await fetch(`/api/patients?${params}`)
      const result: ApiResponse<PaginatedResponse<Patient>> = await response.json()

      if (result.success && result.data) {
        setPatients(result.data.data)
        setTotalPages(result.data.totalPages)
        setCurrentPage(result.data.page)
      } else {
        toast.error(result.error || "获取患者列表失败")
      }
    } catch (error) {
      console.error("获取患者列表失败:", error)
      toast.error("获取患者列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
    fetchPatients(1, value)
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: "",
      age: "",
      gender: "男",
      phone: "",
      idCard: "",
      address: "",
      medicalHistory: "",
      allergies: ""
    })
  }

  // 添加患者
  const handleAddPatient = async () => {
    try {
      if (!formData.name || !formData.age) {
        toast.error("请填写姓名和年龄")
        return
      }

      const response = await fetch("/api/patients", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...formData,
          age: parseInt(formData.age)
        })
      })

      const result: ApiResponse<Patient> = await response.json()

      if (result.success) {
        toast.success("患者添加成功")
        setIsAddDialogOpen(false)
        resetForm()
        fetchPatients(currentPage, searchTerm)
      } else {
        toast.error(result.error || "添加患者失败")
      }
    } catch (error) {
      console.error("添加患者失败:", error)
      toast.error("添加患者失败")
    }
  }

  // 编辑患者
  const handleEditPatient = async () => {
    try {
      if (!editingPatient || !formData.name || !formData.age) {
        toast.error("请填写姓名和年龄")
        return
      }

      const response = await fetch(`/api/patients/${editingPatient.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...formData,
          age: parseInt(formData.age)
        })
      })

      const result: ApiResponse<Patient> = await response.json()

      if (result.success) {
        toast.success("患者信息更新成功")
        setIsEditDialogOpen(false)
        setEditingPatient(null)
        resetForm()
        fetchPatients(currentPage, searchTerm)
      } else {
        toast.error(result.error || "更新患者信息失败")
      }
    } catch (error) {
      console.error("更新患者信息失败:", error)
      toast.error("更新患者信息失败")
    }
  }

  // 删除患者
  const handleDeletePatient = async (patient: Patient) => {
    if (!confirm(`确定要删除患者 ${patient.name} 吗？`)) {
      return
    }

    try {
      const response = await fetch(`/api/patients/${patient.id}`, {
        method: "DELETE"
      })

      const result: ApiResponse<null> = await response.json()

      if (result.success) {
        toast.success("患者删除成功")
        fetchPatients(currentPage, searchTerm)
      } else {
        toast.error(result.error || "删除患者失败")
      }
    } catch (error) {
      console.error("删除患者失败:", error)
      toast.error("删除患者失败")
    }
  }

  // 打开编辑对话框
  const openEditDialog = (patient: Patient) => {
    setEditingPatient(patient)
    setFormData({
      name: patient.name,
      age: patient.age.toString(),
      gender: patient.gender,
      phone: patient.phone || "",
      idCard: patient.idCard || "",
      address: patient.address || "",
      medicalHistory: patient.medicalHistory || "",
      allergies: patient.allergies || ""
    })
    setIsEditDialogOpen(true)
  }

  // 查看患者报告
  const viewPatientReports = (patientId: string) => {
    window.location.href = `/reports?patientId=${patientId}`
  }

  useEffect(() => {
    fetchPatients()
  }, [])

  return (
    <div className="space-y-4">
      {/* 搜索和添加 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索患者姓名、电话或身份证..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { resetForm(); setIsAddDialogOpen(true) }}>
              <Plus className="mr-2 h-4 w-4" />
              添加患者
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加新患者</DialogTitle>
              <DialogDescription>请填写患者的基本信息</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">姓名 *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="请输入患者姓名"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="age">年龄 *</Label>
                  <Input
                    id="age"
                    type="number"
                    value={formData.age}
                    onChange={(e) => setFormData({ ...formData, age: e.target.value })}
                    placeholder="请输入年龄"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gender">性别</Label>
                  <Select value={formData.gender} onValueChange={(value: "男" | "女") => setFormData({ ...formData, gender: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="男">男</SelectItem>
                      <SelectItem value="女">女</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">联系电话</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    placeholder="请输入联系电话"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="idCard">身份证号</Label>
                <Input
                  id="idCard"
                  value={formData.idCard}
                  onChange={(e) => setFormData({ ...formData, idCard: e.target.value })}
                  placeholder="请输入身份证号"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">地址</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  placeholder="请输入地址"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="medicalHistory">病史</Label>
                <Textarea
                  id="medicalHistory"
                  value={formData.medicalHistory}
                  onChange={(e) => setFormData({ ...formData, medicalHistory: e.target.value })}
                  placeholder="请输入病史信息"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allergies">过敏史</Label>
                <Textarea
                  id="allergies"
                  value={formData.allergies}
                  onChange={(e) => setFormData({ ...formData, allergies: e.target.value })}
                  placeholder="请输入过敏史"
                  rows={2}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddPatient}>添加患者</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 患者列表 */}
      <Card>
        <CardHeader>
          <CardTitle>患者列表</CardTitle>
          <CardDescription>管理所有患者的基本信息</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">加载中...</div>
            </div>
          ) : patients.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">暂无患者数据</div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>姓名</TableHead>
                  <TableHead>年龄</TableHead>
                  <TableHead>性别</TableHead>
                  <TableHead>联系电话</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {patients.map((patient) => (
                  <TableRow key={patient.id}>
                    <TableCell className="font-medium">{patient.name}</TableCell>
                    <TableCell>{patient.age}</TableCell>
                    <TableCell>
                      <Badge variant={patient.gender === "男" ? "default" : "secondary"}>
                        {patient.gender}
                      </Badge>
                    </TableCell>
                    <TableCell>{patient.phone || "-"}</TableCell>
                    <TableCell>{new Date(patient.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewPatientReports(patient.id)}
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(patient)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePatient(patient)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchPatients(currentPage - 1, searchTerm)}
                disabled={currentPage <= 1}
              >
                上一页
              </Button>
              <span className="text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchPatients(currentPage + 1, searchTerm)}
                disabled={currentPage >= totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑患者信息</DialogTitle>
            <DialogDescription>修改患者的基本信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">姓名 *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="请输入患者姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-age">年龄 *</Label>
                <Input
                  id="edit-age"
                  type="number"
                  value={formData.age}
                  onChange={(e) => setFormData({ ...formData, age: e.target.value })}
                  placeholder="请输入年龄"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-gender">性别</Label>
                <Select value={formData.gender} onValueChange={(value: "男" | "女") => setFormData({ ...formData, gender: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="男">男</SelectItem>
                    <SelectItem value="女">女</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-phone">联系电话</Label>
                <Input
                  id="edit-phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="请输入联系电话"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-idCard">身份证号</Label>
              <Input
                id="edit-idCard"
                value={formData.idCard}
                onChange={(e) => setFormData({ ...formData, idCard: e.target.value })}
                placeholder="请输入身份证号"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-address">地址</Label>
              <Input
                id="edit-address"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                placeholder="请输入地址"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-medicalHistory">病史</Label>
              <Textarea
                id="edit-medicalHistory"
                value={formData.medicalHistory}
                onChange={(e) => setFormData({ ...formData, medicalHistory: e.target.value })}
                placeholder="请输入病史信息"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-allergies">过敏史</Label>
              <Textarea
                id="edit-allergies"
                value={formData.allergies}
                onChange={(e) => setFormData({ ...formData, allergies: e.target.value })}
                placeholder="请输入过敏史"
                rows={2}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditPatient}>保存修改</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
