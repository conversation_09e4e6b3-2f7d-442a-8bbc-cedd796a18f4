"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CtViewer } from "@/components/ct-viewer"
import { AssessmentReport } from "@/components/assessment-report"
import { ReportChat } from "@/components/report-chat"
import { Loader2, Upload, Plus, Search } from "lucide-react"
import { Patient, ApiResponse, PaginatedResponse, Report } from "@/lib/types"
import { toast } from "sonner"

export function PatientAssessment() {
  const [activeTab, setActiveTab] = React.useState("upload")
  const [isLoading, setIsLoading] = React.useState(false)
  const [ctFile, setCtFile] = React.useState<File | null>(null)
  const [reportData, setReportData] = React.useState<Report | null>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const [error, setError] = React.useState<string | null>(null)

  // 患者相关状态
  const [patients, setPatients] = React.useState<Patient[]>([])
  const [selectedPatient, setSelectedPatient] = React.useState<Patient | null>(null)
  const [patientSearchTerm, setPatientSearchTerm] = React.useState("")
  const [showNewPatientForm, setShowNewPatientForm] = React.useState(false)
  const [patientForm, setPatientForm] = React.useState({
    name: "",
    age: 0,
    gender: "男" as "男" | "女"
  })

  // 获取患者列表
  const fetchPatients = async (search = "") => {
    try {
      const params = new URLSearchParams({
        page: "1",
        limit: "50",
        search,
        sortBy: "name",
        sortOrder: "asc"
      })

      const response = await fetch(`/api/patients?${params}`)
      const result: ApiResponse<PaginatedResponse<Patient>> = await response.json()

      if (result.success && result.data) {
        setPatients(result.data.data)
      }
    } catch (error) {
      console.error("获取患者列表失败:", error)
    }
  }

  // 患者搜索处理
  const handlePatientSearch = (value: string) => {
    setPatientSearchTerm(value)
    fetchPatients(value)
  }

  // 选择患者
  const handleSelectPatient = (patient: Patient) => {
    setSelectedPatient(patient)
    setShowNewPatientForm(false)
    setPatientForm({ name: "", age: 0, gender: "男" })
  }

  // 显示新患者表单
  const handleShowNewPatientForm = () => {
    setSelectedPatient(null)
    setShowNewPatientForm(true)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0]
      if (file.name.endsWith(".nii.gz")) {
        setCtFile(file)
        setError(null)
      } else {
        setCtFile(null)
        setError("请上传 .nii.gz 格式的文件")
      }
    }
  }

  const handleAnalyze = async () => {
    if (!ctFile) {
      setError("请先选择一个CT文件")
      return
    }
    setIsLoading(true)
    setReportData(null)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('file', ctFile)

      // 如果选择了患者，添加患者ID
      if (selectedPatient) {
        formData.append('patientId', selectedPatient.id)
      } else {
        // 如果没有选择患者，添加临时患者信息
        formData.append('patientName', patientForm.name || '未知患者')
        formData.append('patientAge', patientForm.age.toString())
        formData.append('patientGender', patientForm.gender)
      }

      const response = await fetch("/api/analyze-ct", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        let errorDetails = `HTTP error! Status: ${response.status}`
        try {
          const errorData = await response.json()
          errorDetails += ` - ${errorData.error || errorData.message || JSON.stringify(errorData)}`
        } catch (e) {
          const textError = await response.text()
          if (textError) {
            errorDetails += ` - ${textError}`
          }
        }
        console.error("API call failed:", errorDetails)
        throw new Error(`分析失败: ${errorDetails}`)
      }

      const result = await response.json()
      if (result.success) {
        setReportData(result.data)
        setActiveTab("report")
        toast.success("CT分析完成")
      } else {
        throw new Error(result.error || "分析失败")
      }
    } catch (err: any) {
      console.error("分析过程中出现错误:", err)
      const displayError = err.message || "分析过程中出现未知错误，请检查控制台或重试。"
      setError(displayError)
      toast.error(displayError)
    } finally {
      setIsLoading(false)
    }
  }

  // 初始化患者列表
  React.useEffect(() => {
    fetchPatients()
  }, [])

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="upload">1. 上传数据</TabsTrigger>
        <TabsTrigger value="report" disabled={!reportData}>
          2. 查看报告
        </TabsTrigger>
        <TabsTrigger value="chat" disabled={!reportData}>
          3. AI 问答
        </TabsTrigger>
      </TabsList>
      <TabsContent value="upload">
        <div className="space-y-4">
          {/* 患者选择 */}
          <Card>
            <CardHeader>
              <CardTitle>选择患者</CardTitle>
              <CardDescription>请选择现有患者或创建新患者信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索患者姓名..."
                    value={patientSearchTerm}
                    onChange={(e) => handlePatientSearch(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Button variant="outline" onClick={handleShowNewPatientForm}>
                  <Plus className="mr-2 h-4 w-4" />
                  新患者
                </Button>
              </div>

              {/* 患者列表 */}
              {patients.length > 0 && !showNewPatientForm && (
                <div className="max-h-40 overflow-y-auto border rounded-md">
                  {patients.map((patient) => (
                    <div
                      key={patient.id}
                      className={`p-3 cursor-pointer hover:bg-muted ${
                        selectedPatient?.id === patient.id ? "bg-muted" : ""
                      }`}
                      onClick={() => handleSelectPatient(patient)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{patient.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {patient.age}岁 {patient.gender} | {patient.phone || "无电话"}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* 新患者表单 */}
              {showNewPatientForm && (
                <div className="border rounded-md p-4 space-y-4">
                  <h4 className="font-medium">新患者信息</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="patient-name">姓名</Label>
                      <Input
                        id="patient-name"
                        value={patientForm.name}
                        onChange={(e) => setPatientForm({ ...patientForm, name: e.target.value })}
                        placeholder="请输入姓名"
                      />
                    </div>
                    <div>
                      <Label htmlFor="patient-age">年龄</Label>
                      <Input
                        id="patient-age"
                        type="number"
                        value={patientForm.age || ""}
                        onChange={(e) => setPatientForm({ ...patientForm, age: parseInt(e.target.value) || 0 })}
                        placeholder="请输入年龄"
                      />
                    </div>
                    <div>
                      <Label htmlFor="patient-gender">性别</Label>
                      <Select value={patientForm.gender} onValueChange={(value: "男" | "女") => setPatientForm({ ...patientForm, gender: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="男">男</SelectItem>
                          <SelectItem value="女">女</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {/* 选中的患者信息 */}
              {selectedPatient && (
                <div className="bg-muted p-3 rounded-md">
                  <p className="font-medium">已选择患者: {selectedPatient.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedPatient.age}岁 {selectedPatient.gender} | ID: {selectedPatient.id}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* CT文件上传 */}
          <Card>
            <CardHeader>
              <CardTitle>上传 CT 数据</CardTitle>
              <CardDescription>请上传 `.nii.gz` 格式的CT影像文件以进行手术前评估。</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="ct-file">CT 文件</Label>
                <Input id="ct-file" type="file" accept=".nii.gz" onChange={handleFileChange} ref={fileInputRef} />
              </div>
              {ctFile && <p className="text-sm text-muted-foreground">已选择文件: {ctFile.name}</p>}
              {error && <p className="text-sm font-semibold text-red-600">{error}</p>}
              <Button
                onClick={handleAnalyze}
                disabled={!ctFile || isLoading || (!selectedPatient && (!patientForm.name || !patientForm.age))}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    开始分析
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      <TabsContent value="report">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle>CT 影像查看器</CardTitle>
            </CardHeader>
            <CardContent>{ctFile && <CtViewer file={ctFile} />}</CardContent>
          </Card>
          <div className="lg:col-span-3">{reportData && <AssessmentReport data={reportData} />}</div>
        </div>
      </TabsContent>
      <TabsContent value="chat">{reportData && <ReportChat reportSummary={reportData.summary} />}</TabsContent>
    </Tabs>
  )
}
