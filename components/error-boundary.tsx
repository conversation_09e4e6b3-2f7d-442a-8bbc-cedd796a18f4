"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, Home } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })

    // 这里可以添加错误日志上报
    // logErrorToService(error, errorInfo)
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />
      }

      return <DefaultErrorFallback error={this.state.error!} resetError={this.resetError} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error: Error
  resetError: () => void
}

function DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {
  const isDevelopment = process.env.NODE_ENV === "development"

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-2xl">系统出现错误</CardTitle>
          <CardDescription>
            很抱歉，应用程序遇到了一个意外错误。请尝试刷新页面或联系技术支持。
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isDevelopment && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-semibold">错误信息:</p>
                  <p className="text-sm font-mono bg-muted p-2 rounded">{error.message}</p>
                  {error.stack && (
                    <>
                      <p className="font-semibold">错误堆栈:</p>
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                        {error.stack}
                      </pre>
                    </>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={resetError} variant="default">
              <RefreshCw className="mr-2 h-4 w-4" />
              重试
            </Button>
            <Button
              onClick={() => window.location.href = "/"}
              variant="outline"
            >
              <Home className="mr-2 h-4 w-4" />
              返回首页
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新页面
            </Button>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>如果问题持续存在，请联系技术支持</p>
            <p className="mt-1">错误ID: {Date.now().toString(36)}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 用于函数组件的错误边界Hook
export function useErrorHandler() {
  return (error: Error, errorInfo?: { componentStack: string }) => {
    console.error("Uncaught error:", error, errorInfo)
    // 这里可以添加错误上报逻辑
  }
}

// 异步错误处理Hook
export function useAsyncError() {
  const [, setError] = React.useState()
  return React.useCallback(
    (error: Error) => {
      setError(() => {
        throw error
      })
    },
    [setError]
  )
}
