import { NextRequest, NextResponse } from "next/server"
import { PatientStorage, ReportStorage, FileStorage } from "@/lib/storage"
import { ApiResponse, Patient } from "@/lib/types"

// 这是一个模拟的API路由。
// 在实际应用中，您需要处理文件上传（例如使用 formidable 或 busboy），
// 然后将文件发送到您的后端分析服务。
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const patientId = formData.get('patientId') as string
    
    if (!file) {
      const response: ApiResponse<null> = {
        success: false,
        error: '请上传CT文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    if (!file.name.endsWith('.nii.gz')) {
      const response: ApiResponse<null> = {
        success: false,
        error: '请上传 .nii.gz 格式的文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 获取或创建患者信息
    let patient: Patient | null = null
    if (patientId) {
      patient = await PatientStorage.getById(patientId)
      if (!patient) {
        const response: ApiResponse<null> = {
          success: false,
          error: '患者不存在'
        }
        return NextResponse.json(response, { status: 404 })
      }
    } else {
      // 如果没有提供患者ID，创建一个临时患者记录
      const patientName = formData.get('patientName') as string || '未知患者'
      const patientAge = parseInt(formData.get('patientAge') as string || '0')
      const patientGender = (formData.get('patientGender') as string || '男') as '男' | '女'
      
      patient = await PatientStorage.create({
        name: patientName,
        age: patientAge,
        gender: patientGender
      })
    }

    // 保存CT文件
    const filePath = await FileStorage.saveFile(file, 'ct-scans')

    // 模拟处理延迟
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 模拟随机生成结果
    const difficulties: Array<"低" | "中" | "高"> = ["低", "中", "高"]
    const randomDifficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
    const randomScore = Math.floor(Math.random() * 100)

    const keyFindings = [
      "左侧颞叶发现可疑病变区域。",
      "血管分布密集，与关键功能区接近。", 
      "初步判断为高级别胶质瘤。"
    ]

    const recommendations = "建议采用显微神经外科手术，并结合术中神经电生理监测，以最大程度保护神经功能。考虑术前进行功能磁共振成像（fMRI）以精确定位语言和运动中枢。"

    // 创建报告记录
    const report = await ReportStorage.create({
      patientId: patient.id,
      patientInfo: {
        id: patient.id,
        name: patient.name,
        age: patient.age,
        gender: patient.gender
      },
      analysisResults: {
        difficulty: randomDifficulty,
        score: randomScore,
        keyFindings,
        recommendations,
        riskFactors: ["血管密集区域", "功能区邻近", "肿瘤体积较大"],
        surgicalApproach: "显微神经外科手术",
        estimatedDuration: "4-6小时",
        postOpCare: "ICU监护24-48小时，神经功能监测"
      },
      summary: `患者${patient.name}，${patient.age}岁${patient.gender}性，CT影像分析显示左侧颞叶存在可疑病变，血管密集且靠近关键功能区。综合评估手术难度为"${randomDifficulty}"，风险评分为${randomScore}。初步诊断为高级别胶质瘤，建议进行显微手术并辅以术中监测和术前fMRI。`,
      ctFileName: file.name,
      ctFilePath: filePath,
      status: "completed"
    })

    const response: ApiResponse<typeof report> = {
      success: true,
      data: report,
      message: 'CT分析完成'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('CT分析失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || 'CT分析过程中出现错误'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
