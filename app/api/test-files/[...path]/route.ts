import { NextRequest, NextResponse } from "next/server"
import { readFile } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const filePath = params.path.join('/')
    const fullPath = join(process.cwd(), 'data', 'test', filePath)
    
    // 安全检查：确保文件在允许的目录内
    if (!fullPath.startsWith(join(process.cwd(), 'data', 'test'))) {
      return NextResponse.json(
        { error: '访问被拒绝' },
        { status: 403 }
      )
    }
    
    // 检查文件是否存在
    if (!existsSync(fullPath)) {
      return NextResponse.json(
        { error: '文件不存在' },
        { status: 404 }
      )
    }
    
    // 读取文件
    const fileBuffer = await readFile(fullPath)
    
    // 设置响应头
    const headers = new Headers()
    headers.set('Content-Type', 'application/octet-stream')
    headers.set('Content-Disposition', `attachment; filename="${filePath}"`)
    headers.set('Content-Length', fileBuffer.length.toString())
    
    return new NextResponse(fileBuffer, {
      status: 200,
      headers
    })
    
  } catch (error: any) {
    console.error('文件下载失败:', error)
    return NextResponse.json(
      { error: '文件下载失败' },
      { status: 500 }
    )
  }
}
