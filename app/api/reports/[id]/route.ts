import { NextRequest, NextResponse } from 'next/server'
import { ReportStorage } from '@/lib/storage'
import { ApiResponse } from '@/lib/types'

// GET /api/reports/[id] - 获取单个报告详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const report = await ReportStorage.getById(params.id)
    
    if (!report) {
      const response: ApiResponse<null> = {
        success: false,
        error: '报告不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<typeof report> = {
      success: true,
      data: report
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取报告详情失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取报告详情失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/reports/[id] - 更新报告状态或备注
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // 只允许更新特定字段
    const allowedUpdates = ['status', 'notes', 'reviewedBy', 'reviewedAt']
    const updates: any = {}
    
    for (const key of allowedUpdates) {
      if (body[key] !== undefined) {
        updates[key] = body[key]
      }
    }

    // 如果状态更新为已审核，自动设置审核时间
    if (updates.status === 'reviewed' && !updates.reviewedAt) {
      updates.reviewedAt = new Date().toISOString()
    }

    const report = await ReportStorage.update(params.id, updates)
    
    if (!report) {
      const response: ApiResponse<null> = {
        success: false,
        error: '报告不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<typeof report> = {
      success: true,
      data: report,
      message: '报告更新成功'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('更新报告失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '更新报告失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/reports/[id] - 删除报告
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await ReportStorage.delete(params.id)
    
    if (!success) {
      const response: ApiResponse<null> = {
        success: false,
        error: '报告不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<null> = {
      success: true,
      message: '报告删除成功'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('删除报告失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '删除报告失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
