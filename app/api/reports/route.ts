import { NextRequest, NextResponse } from 'next/server'
import { ReportStorage } from '@/lib/storage'
import { PaginationParams, ReportFilters, ApiResponse } from '@/lib/types'

// GET /api/reports - 获取报告列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params: PaginationParams & ReportFilters = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      patientId: searchParams.get('patientId') || undefined,
      status: searchParams.get('status') as any || undefined,
      difficulty: searchParams.get('difficulty') as any || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined
    }

    const result = await ReportStorage.search(params)
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取报告列表失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取报告列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
