import { NextRequest, NextResponse } from "next/server"
import { SegmentationService } from "@/lib/segmentation-service"
import { MeasurementService } from "@/lib/measurement-service"
import { FileStorage } from "@/lib/storage"
import { ApiResponse, DicomCalibration } from "@/lib/types"

// POST /api/segmentation - 执行分割和测量
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const ctFile = formData.get('ctFile') as File
    const labelFile = formData.get('labelFile') as File
    const mode = formData.get('mode') as string // 'auto' 或 'label'
    const modelName = formData.get('modelName') as string || 'tumor-segmentation-v1'
    
    // 验证输入
    if (mode === 'label' && !labelFile) {
      const response: ApiResponse<null> = {
        success: false,
        error: '标签模式下请上传标签文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    if (mode === 'auto' && !ctFile) {
      const response: ApiResponse<null> = {
        success: false,
        error: '自动分割模式下请上传CT文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    console.log(`🔬 开始分割和测量流程 - 模式: ${mode}`)

    let ctFilePath: string | undefined
    let calibration: DicomCalibration

    // 1. 如果有CT文件，保存并提取校准信息
    if (ctFile) {
      ctFilePath = await FileStorage.saveFile(ctFile, 'ct-scans')
      console.log(`💾 CT文件已保存: ${ctFilePath}`)
      calibration = await extractCalibrationFromCT(ctFile)
      console.log(`📏 从CT文件提取校准信息:`, calibration)
    } else {
      // 2. 如果没有CT文件，尝试从标签文件提取校准信息
      console.log(`📏 从标签文件提取校准信息...`)
      calibration = await extractCalibrationFromLabel(labelFile!)
      console.log(`📏 从标签文件提取的校准信息:`, calibration)
    }
    
    let segmentationMask
    
    if (mode === 'auto') {
      // 真实场景：调用分割模型
      console.log(`🤖 [真实场景] 调用自动分割模型: ${modelName}`)
      const ctBuffer = await ctFile.arrayBuffer()
      segmentationMask = await SegmentationService.performSegmentation(
        ctBuffer,
        calibration,
        modelName
      )
    } else {
      // 开发测试：从标签文件加载
      console.log(`📁 [开发测试] 从标签文件加载分割掩码`)
      segmentationMask = await SegmentationService.loadSegmentationFromLabel(
        labelFile!,
        calibration
      )
      
      // 保存标签文件
      const labelFilePath = await FileStorage.saveFile(labelFile!, 'labels')
      console.log(`💾 标签文件已保存: ${labelFilePath}`)
    }
    
    // 3. 计算测量指标
    console.log(`📊 开始计算测量指标...`)
    const measurements = await MeasurementService.calculateAllMeasurements(segmentationMask)
    
    // 4. 返回结果
    const result = {
      segmentation: {
        id: segmentationMask.id,
        labels: segmentationMask.labels,
        dimensions: segmentationMask.dimensions,
        calibration: segmentationMask.calibration,
        modelVersion: segmentationMask.modelVersion,
        confidence: segmentationMask.confidence
      },
      measurements,
      files: {
        ctFile: ctFilePath,
        labelFile: mode === 'label' ? await FileStorage.saveFile(labelFile!, 'labels') : undefined
      }
    }
    
    console.log(`✅ 分割和测量完成，检测到 ${measurements.length} 个结构`)
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: `分割和测量完成，检测到 ${measurements.length} 个结构`
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('分割和测量失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '分割和测量过程中出现错误'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// GET /api/segmentation/models - 获取可用的分割模型
export async function GET(request: NextRequest) {
  try {
    const models = SegmentationService.getAvailableModels()
    
    const response: ApiResponse<typeof models> = {
      success: true,
      data: models
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取模型列表失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取模型列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

/**
 * 从标签文件提取校准信息
 */
async function extractCalibrationFromLabel(labelFile: File): Promise<DicomCalibration> {
  console.log(`📋 从标签文件提取校准信息: ${labelFile.name}`)

  try {
    const arrayBuffer = await labelFile.arrayBuffer()

    // 检查是否为gzip压缩
    const firstBytes = new Uint8Array(arrayBuffer.slice(0, 2))
    let decompressedBuffer = arrayBuffer

    if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
      console.log('检测到gzip压缩，正在解压...')
      decompressedBuffer = await decompressGzip(arrayBuffer)
    }

    // 从NIFTI标签文件提取校准信息
    const calibration = extractNiftiCalibration(decompressedBuffer)

    console.log('从标签文件提取的校准信息:', calibration)
    return calibration

  } catch (error) {
    console.warn('无法从标签文件提取校准信息，使用默认值:', error)

    // 返回默认校准信息
    return {
      pixelSpacing: [1.0, 1.0], // 默认像素间距 (mm)
      sliceThickness: 1.0,      // 默认层厚 (mm)
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  }
}

/**
 * 从CT文件提取DICOM校准信息
 * 注意：这是简化实现，实际应用中需要完整的DICOM解析
 */
async function extractCalibrationFromCT(ctFile: File): Promise<DicomCalibration> {
  console.log(`📋 从CT文件提取校准信息: ${ctFile.name}`)
  
  try {
    const arrayBuffer = await ctFile.arrayBuffer()
    
    // TODO: 实际实现中需要使用专业的DICOM库（如dicom-parser）
    // 这里提供简化的实现示例
    
    // 检查是否为gzip压缩
    const firstBytes = new Uint8Array(arrayBuffer.slice(0, 2))
    let decompressedBuffer = arrayBuffer
    
    if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
      console.log('检测到gzip压缩，正在解压...')
      decompressedBuffer = await decompressGzip(arrayBuffer)
    }
    
    // 简化的NIFTI头部解析（实际应该解析DICOM头部）
    const calibration = extractNiftiCalibration(decompressedBuffer)
    
    console.log('提取的校准信息:', calibration)
    return calibration
    
  } catch (error) {
    console.warn('无法从CT文件提取校准信息，使用默认值:', error)
    
    // 返回默认校准信息
    return {
      pixelSpacing: [0.5, 0.5], // 默认像素间距 (mm)
      sliceThickness: 1.0,      // 默认层厚 (mm)
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  }
}

/**
 * 从NIFTI文件提取校准信息
 */
function extractNiftiCalibration(buffer: ArrayBuffer): DicomCalibration {
  try {
    const view = new DataView(buffer)
    
    // 读取像素维度信息 (NIFTI头部偏移 76-108)
    const pixdim1 = view.getFloat32(80, true)  // x spacing
    const pixdim2 = view.getFloat32(84, true)  // y spacing  
    const pixdim3 = view.getFloat32(88, true)  // z spacing
    
    return {
      pixelSpacing: [
        pixdim1 > 0 ? pixdim1 : 0.5,
        pixdim2 > 0 ? pixdim2 : 0.5
      ],
      sliceThickness: pixdim3 > 0 ? pixdim3 : 1.0,
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  } catch (error) {
    console.warn('NIFTI校准信息解析失败，使用默认值:', error)
    return {
      pixelSpacing: [0.5, 0.5],
      sliceThickness: 1.0,
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  }
}

/**
 * 解压gzip数据 (服务器端)
 */
async function decompressGzip(buffer: ArrayBuffer): Promise<ArrayBuffer> {
  try {
    // 在Node.js环境中使用zlib
    const zlib = await import('zlib')
    const { promisify } = await import('util')
    const gunzip = promisify(zlib.gunzip)

    const compressed = Buffer.from(buffer)
    const decompressed = await gunzip(compressed)
    return decompressed.buffer
  } catch (error) {
    console.warn('Node.js zlib解压失败，尝试备用方案:', error)

    // 备用方案：跳过gzip头部进行简单解析
    return skipGzipHeader(buffer)
  }
}

/**
 * 跳过gzip头部，尝试直接解析 (备用方案)
 */
function skipGzipHeader(buffer: ArrayBuffer): ArrayBuffer {
  const view = new Uint8Array(buffer)

  // 检查gzip魔数
  if (view[0] === 0x1f && view[1] === 0x8b) {
    console.log('检测到gzip格式，尝试跳过头部...')

    // 简单跳过gzip头部
    let offset = 10 // 基本头部长度

    // 跳过额外字段
    if (view[3] & 0x04) {
      const extraLen = view[offset] | (view[offset + 1] << 8)
      offset += 2 + extraLen
    }

    // 跳过文件名
    if (view[3] & 0x08) {
      while (offset < view.length && view[offset] !== 0) offset++
      offset++ // 跳过null终止符
    }

    // 跳过注释
    if (view[3] & 0x10) {
      while (offset < view.length && view[offset] !== 0) offset++
      offset++ // 跳过null终止符
    }

    // 跳过CRC16
    if (view[3] & 0x02) {
      offset += 2
    }

    console.log(`跳过gzip头部，偏移量: ${offset}`)
    // 返回剩余数据（去掉头部和尾部）
    return buffer.slice(offset, buffer.byteLength - 8)
  }

  // 如果不是gzip格式，直接返回原数据
  console.log('非gzip格式，直接返回原数据')
  return buffer
}
