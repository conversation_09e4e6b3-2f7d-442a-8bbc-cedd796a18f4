import { NextRequest, NextResponse } from "next/server"
import { SegmentationService } from "@/lib/segmentation-service"
import { MeasurementService } from "@/lib/measurement-service"
import { FileStorage } from "@/lib/storage"
import { ApiResponse, DicomCalibration } from "@/lib/types"

// POST /api/segmentation - 执行分割和测量
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const ctFile = formData.get('ctFile') as File
    const labelFile = formData.get('labelFile') as File
    const mode = formData.get('mode') as string // 'auto' 或 'label'
    const modelName = formData.get('modelName') as string || 'tumor-segmentation-v1'
    
    // 验证输入
    if (!ctFile) {
      const response: ApiResponse<null> = {
        success: false,
        error: '请上传CT文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    if (mode === 'label' && !labelFile) {
      const response: ApiResponse<null> = {
        success: false,
        error: '标签模式下请上传标签文件'
      }
      return NextResponse.json(response, { status: 400 })
    }

    console.log(`🔬 开始分割和测量流程 - 模式: ${mode}`)
    
    // 1. 保存CT文件
    const ctFilePath = await FileStorage.saveFile(ctFile, 'ct-scans')
    console.log(`💾 CT文件已保存: ${ctFilePath}`)
    
    // 2. 从CT文件提取DICOM校准信息
    const calibration = await extractCalibrationFromCT(ctFile)
    console.log(`📏 校准信息:`, calibration)
    
    let segmentationMask
    
    if (mode === 'auto') {
      // 真实场景：调用分割模型
      console.log(`🤖 [真实场景] 调用自动分割模型: ${modelName}`)
      const ctBuffer = await ctFile.arrayBuffer()
      segmentationMask = await SegmentationService.performSegmentation(
        ctBuffer,
        calibration,
        modelName
      )
    } else {
      // 开发测试：从标签文件加载
      console.log(`📁 [开发测试] 从标签文件加载分割掩码`)
      segmentationMask = await SegmentationService.loadSegmentationFromLabel(
        labelFile!,
        calibration
      )
      
      // 保存标签文件
      const labelFilePath = await FileStorage.saveFile(labelFile!, 'labels')
      console.log(`💾 标签文件已保存: ${labelFilePath}`)
    }
    
    // 3. 计算测量指标
    console.log(`📊 开始计算测量指标...`)
    const measurements = await MeasurementService.calculateAllMeasurements(segmentationMask)
    
    // 4. 返回结果
    const result = {
      segmentation: {
        id: segmentationMask.id,
        labels: segmentationMask.labels,
        dimensions: segmentationMask.dimensions,
        calibration: segmentationMask.calibration,
        modelVersion: segmentationMask.modelVersion,
        confidence: segmentationMask.confidence
      },
      measurements,
      files: {
        ctFile: ctFilePath,
        labelFile: mode === 'label' ? await FileStorage.saveFile(labelFile!, 'labels') : undefined
      }
    }
    
    console.log(`✅ 分割和测量完成，检测到 ${measurements.length} 个结构`)
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: `分割和测量完成，检测到 ${measurements.length} 个结构`
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('分割和测量失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '分割和测量过程中出现错误'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// GET /api/segmentation/models - 获取可用的分割模型
export async function GET(request: NextRequest) {
  try {
    const models = SegmentationService.getAvailableModels()
    
    const response: ApiResponse<typeof models> = {
      success: true,
      data: models
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取模型列表失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取模型列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

/**
 * 从CT文件提取DICOM校准信息
 * 注意：这是简化实现，实际应用中需要完整的DICOM解析
 */
async function extractCalibrationFromCT(ctFile: File): Promise<DicomCalibration> {
  console.log(`📋 从CT文件提取校准信息: ${ctFile.name}`)
  
  try {
    const arrayBuffer = await ctFile.arrayBuffer()
    
    // TODO: 实际实现中需要使用专业的DICOM库（如dicom-parser）
    // 这里提供简化的实现示例
    
    // 检查是否为gzip压缩
    const firstBytes = new Uint8Array(arrayBuffer.slice(0, 2))
    let decompressedBuffer = arrayBuffer
    
    if (firstBytes[0] === 0x1f && firstBytes[1] === 0x8b) {
      console.log('检测到gzip压缩，正在解压...')
      decompressedBuffer = await decompressGzip(arrayBuffer)
    }
    
    // 简化的NIFTI头部解析（实际应该解析DICOM头部）
    const calibration = extractNiftiCalibration(decompressedBuffer)
    
    console.log('提取的校准信息:', calibration)
    return calibration
    
  } catch (error) {
    console.warn('无法从CT文件提取校准信息，使用默认值:', error)
    
    // 返回默认校准信息
    return {
      pixelSpacing: [0.5, 0.5], // 默认像素间距 (mm)
      sliceThickness: 1.0,      // 默认层厚 (mm)
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  }
}

/**
 * 从NIFTI文件提取校准信息
 */
function extractNiftiCalibration(buffer: ArrayBuffer): DicomCalibration {
  try {
    const view = new DataView(buffer)
    
    // 读取像素维度信息 (NIFTI头部偏移 76-108)
    const pixdim1 = view.getFloat32(80, true)  // x spacing
    const pixdim2 = view.getFloat32(84, true)  // y spacing  
    const pixdim3 = view.getFloat32(88, true)  // z spacing
    
    return {
      pixelSpacing: [
        pixdim1 > 0 ? pixdim1 : 0.5,
        pixdim2 > 0 ? pixdim2 : 0.5
      ],
      sliceThickness: pixdim3 > 0 ? pixdim3 : 1.0,
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  } catch (error) {
    console.warn('NIFTI校准信息解析失败，使用默认值:', error)
    return {
      pixelSpacing: [0.5, 0.5],
      sliceThickness: 1.0,
      rescaleSlope: 1.0,
      rescaleIntercept: 0.0
    }
  }
}

/**
 * 解压gzip数据
 */
async function decompressGzip(buffer: ArrayBuffer): Promise<ArrayBuffer> {
  if (typeof window !== 'undefined' && 'DecompressionStream' in window) {
    const stream = new DecompressionStream('gzip')
    const writer = stream.writable.getWriter()
    const reader = stream.readable.getReader()
    
    writer.write(new Uint8Array(buffer))
    writer.close()
    
    const chunks: Uint8Array[] = []
    let result
    while (!(result = await reader.read()).done) {
      chunks.push(result.value)
    }
    
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0)
    const decompressed = new Uint8Array(totalLength)
    let offset = 0
    for (const chunk of chunks) {
      decompressed.set(chunk, offset)
      offset += chunk.length
    }
    
    return decompressed.buffer
  }
  
  throw new Error('浏览器不支持gzip解压缩')
}
