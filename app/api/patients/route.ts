import { NextRequest, NextResponse } from 'next/server'
import { PatientStorage } from '@/lib/storage'
import { PatientFormData, PaginationParams, ApiResponse } from '@/lib/types'

// GET /api/patients - 获取患者列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params: PaginationParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      search: searchParams.get('search') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    }

    const result = await PatientStorage.search(params)
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取患者列表失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取患者列表失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/patients - 创建新患者
export async function POST(request: NextRequest) {
  try {
    const body: PatientFormData = await request.json()
    
    // 验证必填字段
    if (!body.name || !body.age || !body.gender) {
      const response: ApiResponse<null> = {
        success: false,
        error: '姓名、年龄和性别为必填字段'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证年龄范围
    if (body.age < 0 || body.age > 150) {
      const response: ApiResponse<null> = {
        success: false,
        error: '年龄必须在0-150之间'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证性别
    if (!['男', '女'].includes(body.gender)) {
      const response: ApiResponse<null> = {
        success: false,
        error: '性别必须为"男"或"女"'
      }
      return NextResponse.json(response, { status: 400 })
    }

    const patient = await PatientStorage.create(body)
    
    const response: ApiResponse<typeof patient> = {
      success: true,
      data: patient,
      message: '患者创建成功'
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error: any) {
    console.error('创建患者失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '创建患者失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
