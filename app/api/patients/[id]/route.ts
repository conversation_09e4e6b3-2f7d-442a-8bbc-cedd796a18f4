import { NextRequest, NextResponse } from 'next/server'
import { PatientStorage, ReportStorage } from '@/lib/storage'
import { PatientFormData, ApiResponse } from '@/lib/types'

// GET /api/patients/[id] - 获取单个患者信息
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const patient = await PatientStorage.getById(params.id)
    
    if (!patient) {
      const response: ApiResponse<null> = {
        success: false,
        error: '患者不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<typeof patient> = {
      success: true,
      data: patient
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('获取患者信息失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '获取患者信息失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/patients/[id] - 更新患者信息
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body: Partial<PatientFormData> = await request.json()
    
    // 验证年龄范围（如果提供）
    if (body.age !== undefined && (body.age < 0 || body.age > 150)) {
      const response: ApiResponse<null> = {
        success: false,
        error: '年龄必须在0-150之间'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 验证性别（如果提供）
    if (body.gender !== undefined && !['男', '女'].includes(body.gender)) {
      const response: ApiResponse<null> = {
        success: false,
        error: '性别必须为"男"或"女"'
      }
      return NextResponse.json(response, { status: 400 })
    }

    const patient = await PatientStorage.update(params.id, body)
    
    if (!patient) {
      const response: ApiResponse<null> = {
        success: false,
        error: '患者不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<typeof patient> = {
      success: true,
      data: patient,
      message: '患者信息更新成功'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('更新患者信息失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '更新患者信息失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/patients/[id] - 删除患者
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 检查是否有关联的报告
    const reports = await ReportStorage.getByPatientId(params.id)
    if (reports.length > 0) {
      const response: ApiResponse<null> = {
        success: false,
        error: '无法删除患者：存在关联的评估报告'
      }
      return NextResponse.json(response, { status: 400 })
    }

    const success = await PatientStorage.delete(params.id)
    
    if (!success) {
      const response: ApiResponse<null> = {
        success: false,
        error: '患者不存在'
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: ApiResponse<null> = {
      success: true,
      message: '患者删除成功'
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('删除患者失败:', error)
    const response: ApiResponse<null> = {
      success: false,
      error: error.message || '删除患者失败'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
