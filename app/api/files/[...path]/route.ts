import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

// GET /api/files/[...path] - 获取文件
export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const filePath = decodeURIComponent(params.path.join('/'))
    const fullPath = path.join(process.cwd(), 'data', filePath)
    
    // 安全检查：确保文件路径在data目录内
    const dataDir = path.join(process.cwd(), 'data')
    const resolvedPath = path.resolve(fullPath)
    const resolvedDataDir = path.resolve(dataDir)
    
    if (!resolvedPath.startsWith(resolvedDataDir)) {
      return NextResponse.json(
        { error: '无效的文件路径' },
        { status: 403 }
      )
    }

    try {
      const fileBuffer = await fs.readFile(resolvedPath)
      const fileName = path.basename(resolvedPath)
      
      // 根据文件扩展名设置Content-Type
      let contentType = 'application/octet-stream'
      if (fileName.endsWith('.nii.gz')) {
        contentType = 'application/gzip'
      } else if (fileName.endsWith('.json')) {
        contentType = 'application/json'
      } else if (fileName.endsWith('.txt')) {
        contentType = 'text/plain'
      }

      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName}"`,
          'Cache-Control': 'public, max-age=31536000'
        }
      })
    } catch (error) {
      return NextResponse.json(
        { error: '文件不存在' },
        { status: 404 }
      )
    }
  } catch (error: any) {
    console.error('文件服务错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
