import { openai } from "@ai-sdk/openai"
import { streamText } from "ai"
import type { NextRequest } from "next/server"

export const runtime = "edge"

export async function POST(req: NextRequest) {
  try {
    console.log("Chat API called")

    const body = await req.json()
    const { messages } = body

    console.log("Received messages:", messages?.length || 0)

    if (!messages || !Array.isArray(messages)) {
      return new Response("Invalid messages format", { status: 400 })
    }

    // 检查 API Key
    if (!process.env.OPENAI_API_KEY) {
      console.error("OPENAI_API_KEY is not set")
      return new Response("API Key not configured", { status: 500 })
    }

    console.log("Starting streamText with model...")

    const result = await streamText({
      model: openai("gpt-4o-mini", {
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: "https://tbai.xin/v1", // 您的自定义 baseURL
      }),
      messages: messages,
      temperature: 0.7,
      maxTokens: 1000,
    })

    console.log("Streaming response created successfully")
    return result.toAIStreamResponse()
  } catch (error: any) {
    console.error("Chat API error:", error)
    console.error("Error stack:", error.stack)
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        details: error.message,
        stack: error.stack,
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    )
  }
}
