"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Upload, 
  Download, 
  Ruler, 
  TestTube,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react"
import { MeasurementResults } from "@/components/measurement-results"
import { MeasurementResult } from "@/lib/types"
import { toast } from "sonner"

export default function TestMeasurementPage() {
  const [ctFile, setCtFile] = useState<File | null>(null)
  const [labelFile, setLabelFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [measurements, setMeasurements] = useState<MeasurementResult[]>([])
  const [error, setError] = useState<string | null>(null)

  const handleCtFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0]
      const fileName = file.name.toLowerCase()
      if (fileName.endsWith(".nii") || fileName.endsWith(".nii.gz") || fileName.endsWith(".gz")) {
        setCtFile(file)
        setError(null)
        console.log(`CT文件已选择: ${file.name}, 大小: ${file.size} bytes`)
      } else {
        setCtFile(null)
        setError("请上传 .nii 或 .nii.gz 格式的CT文件")
      }
    }
  }

  const handleLabelFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0]
      const fileName = file.name.toLowerCase()
      if (fileName.endsWith(".nii") || fileName.endsWith(".nii.gz") || fileName.endsWith(".gz")) {
        setLabelFile(file)
        setError(null)
        console.log(`标签文件已选择: ${file.name}, 大小: ${file.size} bytes`)
      } else {
        setLabelFile(null)
        setError("请上传 .nii 或 .nii.gz 格式的标签文件")
      }
    }
  }

  const handleMeasurement = async () => {
    if (!labelFile) {
      setError("请上传标签文件")
      return
    }

    setIsLoading(true)
    setMeasurements([])
    setError(null)

    try {
      const formData = new FormData()
      // CT文件是可选的，如果有的话一起上传
      if (ctFile) {
        formData.append('ctFile', ctFile)
      }
      formData.append('labelFile', labelFile)
      formData.append('mode', 'label')

      console.log('发送测量请求...')
      const response = await fetch("/api/segmentation", {
        method: "POST",
        body: formData,
      })

      console.log('响应状态:', response.status)

      if (!response.ok) {
        let errorDetails = `HTTP error! Status: ${response.status}`
        try {
          const errorData = await response.json()
          errorDetails += ` - ${errorData.error || errorData.message || JSON.stringify(errorData)}`
        } catch (e) {
          const textError = await response.text()
          if (textError) {
            errorDetails += ` - ${textError}`
          }
        }
        throw new Error(errorDetails)
      }

      const result = await response.json()
      console.log('测量结果:', result)

      if (result.success) {
        setMeasurements(result.data.measurements)
        toast.success(`测量完成！检测到 ${result.data.measurements.length} 个结构`)
      } else {
        throw new Error(result.error || "测量失败")
      }
    } catch (err: any) {
      console.error("测量失败:", err)
      setError(err.message)
      toast.error(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const downloadTestFile = () => {
    // 下载测试标签文件
    const link = document.createElement('a')
    link.href = '/api/test-files/test-label.nii'
    link.download = 'test-label.nii'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('测试文件下载已开始')
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">测量功能测试</h1>
        <p className="text-muted-foreground">
          测试基于分割掩码的物理尺寸测量功能
        </p>
      </div>

      {/* 测试说明 */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p><strong>测试步骤：</strong></p>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>上传标签文件（.nii或.nii.gz格式）- <strong>必需</strong></li>
              <li>上传CT文件（.nii或.nii.gz格式）- 可选，用于更精确的校准</li>
              <li>点击"开始测量"按钮</li>
              <li>查看测量结果，包括体积、表面积等指标</li>
            </ol>
            <p className="text-xs text-muted-foreground mt-2">
              如果没有测试文件，可以下载我们提供的测试标签文件。
            </p>
          </div>
        </AlertDescription>
      </Alert>

      {/* 文件上传 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>文件上传</span>
          </CardTitle>
          <CardDescription>
            上传分割标签文件进行测量。CT文件可选，用于更精确的校准信息提取。
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="label-file">
                标签文件 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="label-file"
                type="file"
                accept=".nii,.nii.gz,.gz"
                onChange={handleLabelFileChange}
              />
              {labelFile && (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-muted-foreground">{labelFile.name}</span>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                必需：包含分割掩码的NIFTI文件
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ct-file">CT文件 (可选)</Label>
              <Input
                id="ct-file"
                type="file"
                accept=".nii,.nii.gz,.gz"
                onChange={handleCtFileChange}
              />
              {ctFile && (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-muted-foreground">{ctFile.name}</span>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                可选：用于提取更精确的DICOM校准信息
              </p>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <Button
                onClick={handleMeasurement}
                disabled={!labelFile || isLoading}
              >
                {isLoading ? (
                  <>
                    <TestTube className="mr-2 h-4 w-4 animate-spin" />
                    测量中...
                  </>
                ) : (
                  <>
                    <Ruler className="mr-2 h-4 w-4" />
                    开始测量
                  </>
                )}
              </Button>
            </div>

            <Button variant="outline" onClick={downloadTestFile}>
              <Download className="mr-2 h-4 w-4" />
              下载测试文件
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测量结果 */}
      {(measurements.length > 0 || isLoading) && (
        <MeasurementResults
          measurements={measurements}
          isLoading={isLoading}
          onExport={() => {
            const blob = new Blob([JSON.stringify(measurements, null, 2)], {
              type: 'application/json'
            })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `test-measurements-${Date.now()}.json`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            toast.success('测量结果已导出')
          }}
        />
      )}

      {/* 技术说明 */}
      <Card>
        <CardHeader>
          <CardTitle>技术实现说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div>
            <Badge variant="outline" className="mb-2">DICOM校准</Badge>
            <p>从DICOM头文件精确读取Pixel Spacing和Slice Thickness，确保物理尺寸计算的准确性。</p>
          </div>
          <div>
            <Badge variant="outline" className="mb-2">体积计算</Badge>
            <p>体积 = 体素数量 × (spacing_x × spacing_y × spacing_z)，单位为mm³和mL。</p>
          </div>
          <div>
            <Badge variant="outline" className="mb-2">表面积计算</Badge>
            <p>使用Marching Cubes算法重建三角网格，基于网格顶点物理坐标计算表面积。</p>
          </div>
          <div>
            <Badge variant="outline" className="mb-2">质量评估</Badge>
            <p>评估生成网格的顶点数、面数、水密性和平滑度等质量指标。</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
