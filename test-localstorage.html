<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalStorage 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>AI聊天对话记录持久化测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试AI聊天功能的对话记录持久化是否正常工作。</p>
        <p>测试步骤：</p>
        <ol>
            <li>在主页面进行CT分析，生成报告</li>
            <li>在AI聊天中发送一些消息</li>
            <li>切换到其他页面，然后回到主页面</li>
            <li>检查对话记录是否保持</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>LocalStorage 检查工具</h2>
        <button onclick="checkChatSessions()">检查聊天会话</button>
        <button onclick="checkApiConfig()">检查API配置</button>
        <button onclick="clearAllChatData()">清除所有聊天数据</button>
        <button onclick="showAllLocalStorage()">显示所有存储数据</button>
        
        <div id="output" class="output"></div>
    </div>

    <div class="test-section">
        <h2>模拟会话测试</h2>
        <button onclick="createTestSession()">创建测试会话</button>
        <button onclick="loadTestSession()">加载测试会话</button>
        <button onclick="deleteTestSession()">删除测试会话</button>
    </div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        function checkChatSessions() {
            log('检查聊天会话...');
            let sessionCount = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('chat-session-')) {
                    sessionCount++;
                    const data = localStorage.getItem(key);
                    try {
                        const messages = JSON.parse(data);
                        log(`会话 ${key}: ${messages.length} 条消息`);
                    } catch (e) {
                        log(`会话 ${key}: 数据格式错误`);
                    }
                }
            }
            if (sessionCount === 0) {
                log('未找到任何聊天会话');
            }
        }

        function checkApiConfig() {
            log('检查API配置...');
            const config = localStorage.getItem('ai-chat-config');
            if (config) {
                try {
                    const parsed = JSON.parse(config);
                    log(`API配置: ${JSON.stringify(parsed, null, 2)}`);
                } catch (e) {
                    log('API配置数据格式错误');
                }
            } else {
                log('未找到API配置');
            }
        }

        function clearAllChatData() {
            log('清除所有聊天数据...');
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('chat-session-') || key === 'ai-chat-config')) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            log(`已清除 ${keysToRemove.length} 个聊天相关数据项`);
        }

        function showAllLocalStorage() {
            log('显示所有LocalStorage数据...');
            if (localStorage.length === 0) {
                log('LocalStorage为空');
                return;
            }
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                log(`${key}: ${value ? value.substring(0, 100) + (value.length > 100 ? '...' : '') : 'null'}`);
            }
        }

        function createTestSession() {
            log('创建测试会话...');
            const testSessionId = 'chat-session-test-12345';
            const testMessages = [
                {
                    id: 'welcome-message',
                    role: 'assistant',
                    content: '您好，我是您的AI医疗助手。',
                    timestamp: new Date().toISOString()
                },
                {
                    id: 'user-test-1',
                    role: 'user',
                    content: '这是一个测试消息',
                    timestamp: new Date().toISOString()
                },
                {
                    id: 'assistant-test-1',
                    role: 'assistant',
                    content: '这是AI的回复',
                    timestamp: new Date().toISOString()
                }
            ];
            localStorage.setItem(testSessionId, JSON.stringify(testMessages));
            log(`已创建测试会话: ${testSessionId}`);
        }

        function loadTestSession() {
            log('加载测试会话...');
            const testSessionId = 'chat-session-test-12345';
            const data = localStorage.getItem(testSessionId);
            if (data) {
                try {
                    const messages = JSON.parse(data);
                    log(`测试会话包含 ${messages.length} 条消息:`);
                    messages.forEach((msg, index) => {
                        log(`  ${index + 1}. [${msg.role}] ${msg.content}`);
                    });
                } catch (e) {
                    log('测试会话数据格式错误');
                }
            } else {
                log('未找到测试会话');
            }
        }

        function deleteTestSession() {
            log('删除测试会话...');
            const testSessionId = 'chat-session-test-12345';
            localStorage.removeItem(testSessionId);
            log('测试会话已删除');
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('页面加载完成，开始检查LocalStorage状态...');
            checkChatSessions();
            checkApiConfig();
        };
    </script>
</body>
</html>
