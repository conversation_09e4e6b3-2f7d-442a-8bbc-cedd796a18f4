import { promises as fs } from 'fs'
import path from 'path'
import { Patient, Report, PaginatedResponse, PaginationParams, ReportFilters } from './types'

// 数据存储目录
const DATA_DIR = path.join(process.cwd(), 'data')
const PATIENTS_FILE = path.join(DATA_DIR, 'patients.json')
const REPORTS_FILE = path.join(DATA_DIR, 'reports.json')
const UPLOADS_DIR = path.join(DATA_DIR, 'uploads')

// 确保数据目录存在
export async function ensureDataDirectory() {
  try {
    await fs.access(DATA_DIR)
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true })
  }
  
  try {
    await fs.access(UPLOADS_DIR)
  } catch {
    await fs.mkdir(UPLOADS_DIR, { recursive: true })
  }
}

// 读取JSON文件
async function readJsonFile<T>(filePath: string, defaultValue: T): Promise<T> {
  try {
    await ensureDataDirectory()
    const data = await fs.readFile(filePath, 'utf-8')
    return JSON.parse(data)
  } catch {
    return defaultValue
  }
}

// 写入JSON文件
async function writeJsonFile<T>(filePath: string, data: T): Promise<void> {
  await ensureDataDirectory()
  await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
}

// 患者数据操作
export class PatientStorage {
  static async getAll(): Promise<Patient[]> {
    return readJsonFile<Patient[]>(PATIENTS_FILE, [])
  }

  static async getById(id: string): Promise<Patient | null> {
    const patients = await this.getAll()
    return patients.find(p => p.id === id) || null
  }

  static async create(patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<Patient> {
    const patients = await this.getAll()
    const newPatient: Patient = {
      ...patient,
      id: `P${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    patients.push(newPatient)
    await writeJsonFile(PATIENTS_FILE, patients)
    return newPatient
  }

  static async update(id: string, updates: Partial<Omit<Patient, 'id' | 'createdAt'>>): Promise<Patient | null> {
    const patients = await this.getAll()
    const index = patients.findIndex(p => p.id === id)
    if (index === -1) return null

    patients[index] = {
      ...patients[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    await writeJsonFile(PATIENTS_FILE, patients)
    return patients[index]
  }

  static async delete(id: string): Promise<boolean> {
    const patients = await this.getAll()
    const index = patients.findIndex(p => p.id === id)
    if (index === -1) return false

    patients.splice(index, 1)
    await writeJsonFile(PATIENTS_FILE, patients)
    return true
  }

  static async search(params: PaginationParams): Promise<PaginatedResponse<Patient>> {
    const patients = await this.getAll()
    let filtered = patients

    // 搜索过滤
    if (params.search) {
      const searchLower = params.search.toLowerCase()
      filtered = patients.filter(p => 
        p.name.toLowerCase().includes(searchLower) ||
        p.phone?.toLowerCase().includes(searchLower) ||
        p.idCard?.toLowerCase().includes(searchLower)
      )
    }

    // 排序
    if (params.sortBy) {
      filtered.sort((a, b) => {
        const aVal = (a as any)[params.sortBy!]
        const bVal = (b as any)[params.sortBy!]
        const order = params.sortOrder === 'desc' ? -1 : 1
        
        if (aVal < bVal) return -1 * order
        if (aVal > bVal) return 1 * order
        return 0
      })
    }

    // 分页
    const total = filtered.length
    const totalPages = Math.ceil(total / params.limit)
    const start = (params.page - 1) * params.limit
    const data = filtered.slice(start, start + params.limit)

    return {
      data,
      total,
      page: params.page,
      limit: params.limit,
      totalPages
    }
  }
}

// 报告数据操作
export class ReportStorage {
  static async getAll(): Promise<Report[]> {
    return readJsonFile<Report[]>(REPORTS_FILE, [])
  }

  static async getById(id: string): Promise<Report | null> {
    const reports = await this.getAll()
    return reports.find(r => r.id === id) || null
  }

  static async getByPatientId(patientId: string): Promise<Report[]> {
    const reports = await this.getAll()
    return reports.filter(r => r.patientId === patientId)
  }

  static async create(report: Omit<Report, 'id' | 'createdAt' | 'updatedAt'>): Promise<Report> {
    const reports = await this.getAll()
    const newReport: Report = {
      ...report,
      id: `R${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    reports.push(newReport)
    await writeJsonFile(REPORTS_FILE, reports)
    return newReport
  }

  static async update(id: string, updates: Partial<Omit<Report, 'id' | 'createdAt'>>): Promise<Report | null> {
    const reports = await this.getAll()
    const index = reports.findIndex(r => r.id === id)
    if (index === -1) return null

    reports[index] = {
      ...reports[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    await writeJsonFile(REPORTS_FILE, reports)
    return reports[index]
  }

  static async delete(id: string): Promise<boolean> {
    const reports = await this.getAll()
    const index = reports.findIndex(r => r.id === id)
    if (index === -1) return false

    reports.splice(index, 1)
    await writeJsonFile(REPORTS_FILE, reports)
    return true
  }

  static async search(params: PaginationParams & ReportFilters): Promise<PaginatedResponse<Report>> {
    const reports = await this.getAll()
    let filtered = reports

    // 应用筛选条件
    if (params.patientId) {
      filtered = filtered.filter(r => r.patientId === params.patientId)
    }
    if (params.status) {
      filtered = filtered.filter(r => r.status === params.status)
    }
    if (params.difficulty) {
      filtered = filtered.filter(r => r.analysisResults.difficulty === params.difficulty)
    }
    if (params.dateFrom) {
      filtered = filtered.filter(r => r.createdAt >= params.dateFrom!)
    }
    if (params.dateTo) {
      filtered = filtered.filter(r => r.createdAt <= params.dateTo!)
    }

    // 搜索过滤
    if (params.search) {
      const searchLower = params.search.toLowerCase()
      filtered = filtered.filter(r => 
        r.patientInfo.name.toLowerCase().includes(searchLower) ||
        r.summary.toLowerCase().includes(searchLower) ||
        r.id.toLowerCase().includes(searchLower)
      )
    }

    // 排序（默认按创建时间倒序）
    filtered.sort((a, b) => {
      if (params.sortBy) {
        const aVal = (a as any)[params.sortBy]
        const bVal = (b as any)[params.sortBy]
        const order = params.sortOrder === 'desc' ? -1 : 1
        
        if (aVal < bVal) return -1 * order
        if (aVal > bVal) return 1 * order
        return 0
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // 分页
    const total = filtered.length
    const totalPages = Math.ceil(total / params.limit)
    const start = (params.page - 1) * params.limit
    const data = filtered.slice(start, start + params.limit)

    return {
      data,
      total,
      page: params.page,
      limit: params.limit,
      totalPages
    }
  }
}

// 文件存储操作
export class FileStorage {
  static async saveFile(file: File, subDir?: string): Promise<string> {
    await ensureDataDirectory()
    
    const targetDir = subDir ? path.join(UPLOADS_DIR, subDir) : UPLOADS_DIR
    try {
      await fs.access(targetDir)
    } catch {
      await fs.mkdir(targetDir, { recursive: true })
    }

    const fileName = `${Date.now()}-${file.name}`
    const filePath = path.join(targetDir, fileName)
    
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(filePath, buffer)
    
    return path.relative(DATA_DIR, filePath)
  }

  static async deleteFile(relativePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(DATA_DIR, relativePath)
      await fs.unlink(fullPath)
      return true
    } catch {
      return false
    }
  }

  static getFileUrl(relativePath: string): string {
    return `/api/files/${encodeURIComponent(relativePath)}`
  }
}
