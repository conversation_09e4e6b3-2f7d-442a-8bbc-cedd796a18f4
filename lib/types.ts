// 患者信息类型定义
export interface Patient {
  id: string
  name: string
  age: number
  gender: "男" | "女"
  phone?: string
  idCard?: string
  address?: string
  medicalHistory?: string
  allergies?: string
  createdAt: string
  updatedAt: string
}

// 分析结果类型定义
export interface AnalysisResults {
  difficulty: "低" | "中" | "高"
  score: number
  keyFindings: string[]
  recommendations: string
  riskFactors?: string[]
  surgicalApproach?: string
  estimatedDuration?: string
  postOpCare?: string
}

// 报告数据类型定义
export interface Report {
  id: string
  patientId: string
  patientInfo: {
    id: string
    name: string
    age: number
    gender: string
  }
  analysisResults: AnalysisResults
  summary: string
  ctFileName?: string
  ctFilePath?: string
  status: "pending" | "completed" | "reviewed"
  createdAt: string
  updatedAt: string
  reviewedBy?: string
  reviewedAt?: string
  notes?: string
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  limit: number
  search?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 患者创建/更新表单数据
export interface PatientFormData {
  name: string
  age: number
  gender: "男" | "女"
  phone?: string
  idCard?: string
  address?: string
  medicalHistory?: string
  allergies?: string
}

// 报告筛选参数
export interface ReportFilters {
  patientId?: string
  status?: "pending" | "completed" | "reviewed"
  difficulty?: "低" | "中" | "高"
  dateFrom?: string
  dateTo?: string
}

// 统计数据类型
export interface Statistics {
  totalPatients: number
  totalReports: number
  reportsToday: number
  reportsThisWeek: number
  reportsThisMonth: number
  difficultyDistribution: {
    low: number
    medium: number
    high: number
  }
  statusDistribution: {
    pending: number
    completed: number
    reviewed: number
  }
}

// DICOM校准数据类型
export interface DicomCalibration {
  pixelSpacing: [number, number] // [x, y] spacing in mm
  sliceThickness: number // z spacing in mm
  imageOrientation?: number[] // Image Orientation Patient
  imagePosition?: number[] // Image Position Patient
  rescaleSlope?: number // Rescale Slope
  rescaleIntercept?: number // Rescale Intercept
}

// 分割掩码类型
export interface SegmentationMask {
  id: string
  data: Uint8Array | Uint16Array // 分割掩码数据
  dimensions: [number, number, number] // [width, height, depth]
  labels: { [key: number]: string } // 标签映射 {1: "tumor", 2: "organ", ...}
  calibration: DicomCalibration
  createdAt: string
  modelVersion?: string
  confidence?: number
}

// 测量结果类型
export interface MeasurementResult {
  id: string
  segmentationId: string
  labelId: number
  labelName: string
  measurements: {
    volume: {
      voxelCount: number
      physicalVolume: number // mm³
      physicalVolumeML: number // mL
    }
    surfaceArea: {
      physicalArea: number // mm²
      physicalAreaCM2: number // cm²
    }
    boundingBox: {
      min: [number, number, number] // [x, y, z] in voxel coordinates
      max: [number, number, number] // [x, y, z] in voxel coordinates
      physicalSize: [number, number, number] // [width, height, depth] in mm
    }
    centroid: {
      voxelCoords: [number, number, number]
      physicalCoords: [number, number, number] // mm
    }
  }
  quality: {
    meshVertices: number
    meshFaces: number
    isWatertight: boolean
    smoothness: number
  }
  createdAt: string
}

// 分割模型配置
export interface SegmentationModelConfig {
  modelName: string
  modelVersion: string
  inputSize: [number, number, number]
  outputClasses: { [key: number]: string }
  preprocessing: {
    normalize: boolean
    resample: boolean
    targetSpacing?: [number, number, number]
  }
  postprocessing: {
    fillHoles: boolean
    smoothing: boolean
    minComponentSize: number
    connectivityFilter: boolean
  }
}

// 分析任务状态
export interface AnalysisTask {
  id: string
  reportId: string
  status: "pending" | "segmenting" | "measuring" | "completed" | "failed"
  progress: {
    currentStep: string
    percentage: number
    estimatedTimeRemaining?: number
  }
  segmentation?: SegmentationMask
  measurements?: MeasurementResult[]
  error?: string
  createdAt: string
  completedAt?: string
}
