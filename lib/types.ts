// 患者信息类型定义
export interface Patient {
  id: string
  name: string
  age: number
  gender: "男" | "女"
  phone?: string
  idCard?: string
  address?: string
  medicalHistory?: string
  allergies?: string
  createdAt: string
  updatedAt: string
}

// 分析结果类型定义
export interface AnalysisResults {
  difficulty: "低" | "中" | "高"
  score: number
  keyFindings: string[]
  recommendations: string
  riskFactors?: string[]
  surgicalApproach?: string
  estimatedDuration?: string
  postOpCare?: string
}

// 报告数据类型定义
export interface Report {
  id: string
  patientId: string
  patientInfo: {
    id: string
    name: string
    age: number
    gender: string
  }
  analysisResults: AnalysisResults
  summary: string
  ctFileName?: string
  ctFilePath?: string
  status: "pending" | "completed" | "reviewed"
  createdAt: string
  updatedAt: string
  reviewedBy?: string
  reviewedAt?: string
  notes?: string
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  limit: number
  search?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 患者创建/更新表单数据
export interface PatientFormData {
  name: string
  age: number
  gender: "男" | "女"
  phone?: string
  idCard?: string
  address?: string
  medicalHistory?: string
  allergies?: string
}

// 报告筛选参数
export interface ReportFilters {
  patientId?: string
  status?: "pending" | "completed" | "reviewed"
  difficulty?: "低" | "中" | "高"
  dateFrom?: string
  dateTo?: string
}

// 统计数据类型
export interface Statistics {
  totalPatients: number
  totalReports: number
  reportsToday: number
  reportsThisWeek: number
  reportsThisMonth: number
  difficultyDistribution: {
    low: number
    medium: number
    high: number
  }
  statusDistribution: {
    pending: number
    completed: number
    reviewed: number
  }
}
