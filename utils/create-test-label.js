/**
 * 创建测试用的NIFTI标签文件
 * 这个脚本可以生成一个简单的.nii文件用于测试
 */

const fs = require('fs')
const path = require('path')

function createNiftiHeader() {
  // 创建348字节的NIFTI-1头部
  const header = Buffer.alloc(348)
  
  // 头部大小 (偏移0)
  header.writeInt32LE(348, 0)
  
  // 数据类型 (偏移70) - 2 = unsigned char
  header.writeInt16LE(2, 70)
  
  // 位深度 (偏移72) - 8 bits
  header.writeInt16LE(8, 72)
  
  // 维度数量 (偏移40) - 3D
  header.writeInt16LE(3, 40)
  
  // 各维度大小 (偏移42-56)
  header.writeInt16LE(64, 42)  // x维度
  header.writeInt16LE(64, 44)  // y维度  
  header.writeInt16LE(32, 46)  // z维度
  header.writeInt16LE(1, 48)   // t维度
  
  // 像素维度 (偏移76-108)
  header.writeFloatLE(1.0, 80)  // x spacing
  header.writeFloatLE(1.0, 84)  // y spacing
  header.writeFloatLE(2.0, 88)  // z spacing
  
  // 体素偏移 (偏移108)
  header.writeFloatLE(352, 108)
  
  // 缩放参数
  header.writeFloatLE(1.0, 112) // scl_slope
  header.writeFloatLE(0.0, 116) // scl_inter
  
  // NIFTI魔数 (偏移344)
  header.write('n+1\0', 344)
  
  return header
}

function createTestLabelData() {
  const width = 64
  const height = 64
  const depth = 32
  
  const data = Buffer.alloc(width * height * depth)
  
  // 创建一些测试结构
  
  // 结构1: 中心的立方体
  for (let z = 10; z < 20; z++) {
    for (let y = 20; y < 40; y++) {
      for (let x = 20; x < 40; x++) {
        const index = z * width * height + y * width + x
        data[index] = 1
      }
    }
  }
  
  // 结构2: 左侧的球体
  const centerX = 15, centerY = 30, centerZ = 15
  const radius = 8
  for (let z = 5; z < 25; z++) {
    for (let y = 20; y < 40; y++) {
      for (let x = 5; x < 25; x++) {
        const distance = Math.sqrt(
          (x - centerX) ** 2 + 
          (y - centerY) ** 2 + 
          (z - centerZ) ** 2
        )
        if (distance <= radius) {
          const index = z * width * height + y * width + x
          data[index] = 2
        }
      }
    }
  }
  
  // 结构3: 右侧的椭球体
  const centerX2 = 45, centerY2 = 30, centerZ2 = 20
  for (let z = 10; z < 30; z++) {
    for (let y = 20; y < 40; y++) {
      for (let x = 35; x < 55; x++) {
        const dx = (x - centerX2) / 8
        const dy = (y - centerY2) / 6
        const dz = (z - centerZ2) / 4
        if (dx*dx + dy*dy + dz*dz <= 1) {
          const index = z * width * height + y * width + x
          data[index] = 3
        }
      }
    }
  }
  
  return data
}

function createTestNiftiFile() {
  const header = createNiftiHeader()
  const data = createTestLabelData()
  
  // 添加4字节的扩展 (使总偏移为352)
  const extension = Buffer.alloc(4)
  
  const niftiFile = Buffer.concat([header, extension, data])
  
  // 确保data目录存在
  const dataDir = path.join(__dirname, '..', 'data', 'test')
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
  }
  
  const filePath = path.join(dataDir, 'test-label.nii')
  fs.writeFileSync(filePath, niftiFile)
  
  console.log(`测试标签文件已创建: ${filePath}`)
  console.log(`文件大小: ${niftiFile.length} 字节`)
  console.log(`头部大小: ${header.length} 字节`)
  console.log(`数据大小: ${data.length} 字节`)
  
  // 创建一些统计信息
  const labelCounts = {}
  for (let i = 0; i < data.length; i++) {
    const value = data[i]
    labelCounts[value] = (labelCounts[value] || 0) + 1
  }
  
  console.log('\n标签统计:')
  Object.entries(labelCounts).forEach(([label, count]) => {
    if (parseInt(label) > 0) {
      console.log(`  标签 ${label}: ${count} 体素`)
    }
  })
  
  return filePath
}

// 如果直接运行此脚本
if (require.main === module) {
  createTestNiftiFile()
}

module.exports = { createTestNiftiFile }
